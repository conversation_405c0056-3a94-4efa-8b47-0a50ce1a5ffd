#!/usr/bin/env python3
"""
完整修复 kubernetes-client-java 24.0.0 版本的 API 调用
"""

import os
import re
import glob

def complete_fix_in_file(file_path):
    """完整修复单个文件"""
    print(f"正在完整修复: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 1. 为所有 API 调用添加 .execute()
    # 匹配模式：变量 = api.方法名(参数)
    api_patterns = [
        # 基本的 API 调用模式
        r'(\w+\s*=\s*api\.list[A-Z]\w*\([^)]*\))(?!\.execute\(\))',
        r'(\w+\s*=\s*api\.read[A-Z]\w*\([^)]*\))(?!\.execute\(\))',
        r'(\w+\s*=\s*api\.delete[A-Z]\w*\([^)]*\))(?!\.execute\(\))',
        r'(\w+\s*=\s*api\.patch[A-Z]\w*\([^)]*\))(?!\.execute\(\))',
        r'(\w+\s*=\s*api\.replace[A-Z]\w*\([^)]*\))(?!\.execute\(\))',
        r'(\w+\s*=\s*api\.create[A-Z]\w*\([^)]*\))(?!\.execute\(\))',
    ]
    
    for pattern in api_patterns:
        content = re.sub(pattern, r'\1.execute()', content)
    
    # 2. 修复 Kubectl 调用
    kubectl_patterns = [
        r'(\w+\s*=\s*Kubectl\.cordon\([^)]*\))(?!\.execute\(\))',
        r'(\w+\s*=\s*Kubectl\.uncordon\([^)]*\))(?!\.execute\(\))',
        r'(\w+\s*=\s*Kubectl\.drain\([^)]*\))(?!\.execute\(\))',
    ]
    
    for pattern in kubectl_patterns:
        content = re.sub(pattern, r'\1.execute()', content)
    
    # 3. 修复一些特殊情况
    # 移除在字符串或其他非 API 对象上的 .execute() 调用
    content = re.sub(r'k8sQuery\.getNamespace\(\)\.execute\(\)', 'k8sQuery.getNamespace()', content)
    content = re.sub(r'toJSONString\([^)]+\)\.execute\(\)', lambda m: m.group(0).replace('.execute()', ''), content)
    
    # 4. 修复一些错误的变量赋值
    # 移除在已经是对象的变量上的 .execute()
    content = re.sub(r'(V1\w+List\s+\w+\s*=\s*[^;]+)\.execute\(\);', r'\1;', content)
    content = re.sub(r'(V1\w+\s+\w+\s*=\s*[^;]+)\.execute\(\);', r'\1;', content)
    content = re.sub(r'(V1Status\s+\w+\s*=\s*[^;]+)\.execute\(\);', r'\1;', content)
    content = re.sub(r'(\w+List\s+\w+\s*=\s*[^;]+)\.execute\(\);', r'\1;', content)
    
    # 保存修改后的文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✓ 已完整修复 {file_path}")
        return True
    else:
        print(f"  - 无需修改 {file_path}")
        return False

def main():
    """主函数"""
    # 查找所有需要修复的 Java 文件
    java_files = glob.glob("skynet-platform-xmanager/src/main/java/skynet/platform/manager/k8s/service/impl/*.java")
    
    fixed_count = 0
    total_count = len(java_files)
    
    print(f"找到 {total_count} 个 Java 文件需要完整修复...")
    
    for java_file in java_files:
        if complete_fix_in_file(java_file):
            fixed_count += 1
    
    print(f"\n完整修复完成！共修复了 {fixed_count}/{total_count} 个文件。")

if __name__ == "__main__":
    main()

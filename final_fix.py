#!/usr/bin/env python3
"""
最终修复脚本
"""

import os
import re
import glob

def final_fix_in_file(file_path):
    """最终修复单个文件"""
    print(f"正在最终修复: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 1. 修复错误的 .execute() 调用
    # 移除在非 API 调用上的 .execute()
    content = re.sub(r'(V1\w+List\s+\w+\s*=\s*[^;]+)\.execute\(\);', r'\1;', content)
    content = re.sub(r'(V1\w+\s+\w+\s*=\s*[^;]+)\.execute\(\);', r'\1;', content)
    content = re.sub(r'(V1Status\s+\w+\s*=\s*[^;]+)\.execute\(\);', r'\1;', content)
    content = re.sub(r'(\w+List\s+\w+\s*=\s*[^;]+)\.execute\(\);', r'\1;', content)
    
    # 2. 修复 patch 方法的参数
    # 移除多余的 null 参数
    patch_fixes = [
        (r'api\.patchNamespacedDeployment\(\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null\s*\)', r'api.patchNamespacedDeployment(\1, \2, \3).execute()'),
        (r'api\.patchNamespacedDaemonSet\(\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null\s*\)', r'api.patchNamespacedDaemonSet(\1, \2, \3).execute()'),
        (r'api\.patchNamespacedStatefulSet\(\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null\s*\)', r'api.patchNamespacedStatefulSet(\1, \2, \3).execute()'),
        (r'api\.patchNamespacedCronJob\(\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null\s*\)', r'api.patchNamespacedCronJob(\1, \2, \3).execute()'),
    ]
    
    for pattern, replacement in patch_fixes:
        content = re.sub(pattern, replacement, content)
    
    # 3. 修复一些特殊的错误调用
    # 修复在字符串上调用 .execute() 的错误
    content = re.sub(r'k8sQuery\.getNamespace\(\)\.execute\(\)', 'k8sQuery.getNamespace()', content)
    
    # 保存修改后的文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✓ 已最终修复 {file_path}")
        return True
    else:
        print(f"  - 无需修改 {file_path}")
        return False

def main():
    """主函数"""
    # 查找所有需要修复的 Java 文件
    java_files = glob.glob("skynet-platform-xmanager/src/main/java/skynet/platform/manager/k8s/service/impl/*.java")
    
    fixed_count = 0
    total_count = len(java_files)
    
    print(f"找到 {total_count} 个 Java 文件需要最终修复...")
    
    for java_file in java_files:
        if final_fix_in_file(java_file):
            fixed_count += 1
    
    print(f"\n最终修复完成！共修复了 {fixed_count}/{total_count} 个文件。")

if __name__ == "__main__":
    main()

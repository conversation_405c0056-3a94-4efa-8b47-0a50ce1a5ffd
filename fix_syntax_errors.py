#!/usr/bin/env python3
"""
修复脚本生成的语法错误
"""

import os
import re
import glob

def fix_syntax_errors_in_file(file_path):
    """修复单个文件中的语法错误"""
    print(f"正在修复语法错误: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 修复错误的方法调用语法
    # 例如: api.listNamespacedJob(namespace), -> api.listNamespacedJob(namespace);
    content = re.sub(
        r'(\w+\.list\w+\([^)]*\)),\s*null[^;]*;',
        r'\1;',
        content
    )
    
    # 修复错误的方法调用语法 - 另一种模式
    # 例如: api.listJobForAllNamespaces(), k8sQuery.getLabelSelector(), null, null, null, null, null, null);
    content = re.sub(
        r'(\w+\.list\w+\([^)]*\)),\s*[^;]*;',
        r'\1;',
        content
    )
    
    # 修复错误的方法调用语法 - 第三种模式
    # 例如: api.listNamespacedJob(namespace), null, null, null, k8sQuery.getFieldSelector(), k8sQuery.getLabelSelector(), null, null, null, null, null);
    content = re.sub(
        r'(\w+\.list\w+\([^)]*\)),\s*null,\s*null,\s*null,\s*[^;]*;',
        r'\1;',
        content
    )
    
    # 保存修改后的文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✓ 已修复语法错误 {file_path}")
        return True
    else:
        print(f"  - 无语法错误 {file_path}")
        return False

def main():
    """主函数"""
    # 查找所有需要修复的 Java 文件
    java_files = glob.glob("skynet-platform-xmanager/src/main/java/skynet/platform/manager/k8s/service/impl/*.java")
    
    fixed_count = 0
    total_count = len(java_files)
    
    print(f"找到 {total_count} 个 Java 文件需要检查语法错误...")
    
    for java_file in java_files:
        if fix_syntax_errors_in_file(java_file):
            fixed_count += 1
    
    print(f"\n语法错误修复完成！共修复了 {fixed_count}/{total_count} 个文件。")

if __name__ == "__main__":
    main()

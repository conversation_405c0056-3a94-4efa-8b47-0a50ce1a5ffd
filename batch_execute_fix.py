#!/usr/bin/env python3
"""
批量添加 .execute() 修复脚本
"""

import os
import re
import glob

def batch_execute_fix_in_file(file_path):
    """批量修复单个文件中的 .execute() 调用"""
    print(f"正在批量修复: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 精确匹配需要添加 .execute() 的模式
    # 匹配：变量 = api.方法(参数);
    patterns = [
        # 基本的 API 调用模式 - 更精确的匹配
        r'(\s+)(\w+\s+\w+\s*=\s*api\.[a-zA-Z][a-zA-Z0-9]*\([^)]*\))(\s*;)',
        # Kubectl 调用模式
        r'(\s+)(\w+\s+\w+\s*=\s*Kubectl\.[a-zA-Z][a-zA-Z0-9]*\([^)]*\))(\s*;)',
    ]
    
    for pattern in patterns:
        # 只为没有 .execute() 的调用添加
        def replace_func(match):
            prefix = match.group(1)
            api_call = match.group(2)
            suffix = match.group(3)
            
            # 如果已经有 .execute()，不修改
            if '.execute()' in api_call:
                return match.group(0)
            
            # 添加 .execute()
            return f"{prefix}{api_call}.execute(){suffix}"
        
        content = re.sub(pattern, replace_func, content)
    
    # 修复一些特殊情况
    # 移除在字符串或其他非 API 对象上的 .execute() 调用
    content = re.sub(r'k8sQuery\.getNamespace\(\)\.execute\(\)', 'k8sQuery.getNamespace()', content)
    content = re.sub(r'toJSONString\([^)]+\)\.execute\(\)', lambda m: m.group(0).replace('.execute()', ''), content)
    content = re.sub(r'patch\.toString\(\)\.execute\(\)', 'patch.toString()', content)
    
    # 保存修改后的文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✓ 已批量修复 {file_path}")
        return True
    else:
        print(f"  - 无需修改 {file_path}")
        return False

def main():
    """主函数"""
    # 查找所有需要修复的 Java 文件
    java_files = glob.glob("skynet-platform-xmanager/src/main/java/skynet/platform/manager/k8s/service/impl/*.java")
    
    fixed_count = 0
    total_count = len(java_files)
    
    print(f"找到 {total_count} 个 Java 文件需要批量修复...")
    
    for java_file in java_files:
        if batch_execute_fix_in_file(java_file):
            fixed_count += 1
    
    print(f"\n批量修复完成！共修复了 {fixed_count}/{total_count} 个文件。")

if __name__ == "__main__":
    main()

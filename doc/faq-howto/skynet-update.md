<h1>skynet升级</h1>

# 停止服务
先结束所有skynet-xagent服务。如果被托管服务可以被停止，可以直接在skynet-xmanager上点击`'停止服务器'`。如果被托管服务不能被停止，需要登陆每个节点，到${SKYNET_HOME}/bin/目录下，执行 `'./ant-xagent.sh kill'`。

再停止skynet-xmanager，到${SKYNET_HOME}/bin/目录下，执行 `'./ant-xmanager.sh stop'`

# 备份数据

在skynet-xmanager节点上，备份原来的skynet目录。
```
mv /iflytek/server/skynet /iflytek/server/skynet-backup
```

# 解压升级包，还原数据

数据还原
```
 tar -zxvf ./skynet-3.0.0-SNAPSHOT-x86_64.tar.gz -C /iflytek/server/
 mv /iflytek/server/skynet-backup/repo/* /iflytek/server/skynet/repo/
 mv /iflytek/server/skynet-backup/plugin/* /iflytek/server/skynet/plugin/
```

配置还原：

如果新版本配置和上一个版本没有差异，可以直接用mv还原之前的配置文件
```
 mv /iflytek/server/skynet-backup/conf/* /iflytek/server/skynet/conf/
```

如果新版本配置和旧版本不兼容，需要修改配置文件，具体参考特定的版本的升级文档。

# 推送到所有skynet-xagent节点

启动ant-xmanager: `'./ant-xmanager daemon'`

在 `'服务管理'`界面，所有的服务器节点，点击`'操作->编辑服务器'`，重新注册服务器，就会推送更新到该节点。

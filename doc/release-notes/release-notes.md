
<h1>Release Notes</h1>

<!-- TOC -->
- [3.0.0-SNAPSHOT](#211012release)
- [2.1.1011.RELEASE](#211011release)
- [2.1.1010-RELEASE](#211010-release)
- [2.1.1009.4.RELEASE](#2110094release)
- [2.1.1009.3.RELEASE](#2110093release)
- [2.1.1009.2.RELEASE](#2110092release)
- [2.1.1009.1.RELEASE](#2110091release)
- [2.1.1009.RELEASE](#211009release)
- [2.1.1008](#211008)
- [2.1.1007](#211007)
- [2.1.1006](#211006)

<!-- /TOC -->

# 3.0.0-SNAPSHOT

> 发布时间：[2020年06月18日]

- 功能优化
    - 日志采集，支持所有托管类型的Boot控制台标准输出日志的存储和统一采集到ELK。
    - 服务托管：DockerBoot 参数支持启动参数和运行参数（新增），同时支持同一个docker可以启动多个实例。
    - 服务定义：移除Eureka服务发现配置，以后业务根据需求自行在属性中配置。
    - 服务启动：服务启动参数 针对SpringBoot fatJar 增加manifest元数据信息展示，方便版本查看。 
    - xmansger：服务IP地址如是回调地址，通过Spring.cloud.client.ipAddress 获取本机实际IP，汇报到服务发现中心。
    - xmansger：下载文件失败日志简化处理，排除了日志堆栈信息，减少日志体检。
    - xmansger: jquery 升级到 3.5.1            
    - 服务发现：支持SpringCloudZookeeperDiscovery规范，自动将托管的服务注册汇报到zookeeper,方便SpringCloud服务发现规范访问Skynet托管的服务。缺省zkpath=/skynet/discovery    
- 缺陷修复
    - 修订：日志输出页面标题，由Skynet标注输出改为Skynet标准输出

# 2.1.1011.RELEASE

> 发布时间：2020年04月17日

- 缺陷修复
    - 修复在某些国产化平台（银河麒麟OS + FT2000 CPU)上xagent崩溃问题。

# 2.1.1010-RELEASE

> 发布时间：2020年03月06日

- 功能优化
    - springboot服务设置了server.context-path时，能相应调整xmanager界面获取actuator mapping的链接URL path
- 缺陷修复
    - 修复通过命令行./ant-xagent.sh stop命令停止xagent及被托管服务时，被托管服务反复重启的bug
	- 修复因为jar包加载顺序不固定导致的在某些环境下无法打开xmanager界面的问题（HTTP 500: Error resolving 'v2/login',template might not exist or...)
- 安全漏洞修复
    - 修复Tomcat中存在的文件读取/包含漏洞，升级 Apache Tomcat 到 8.5.51。


# 2.1.1009.4.RELEASE

> 发布时间：2019年12月26日

功能优化
  - 服务托管，增加随机端口区间，缺省区间：[22300,32300]（可以通过 skynet.random.port.range.begin/skynet.random.port.range.end 属性设置）
	- 服务托管，优化日志配置选项，logging.path、logging.file 以外围配置优先，如果没有配置将采用默认。
	- 服务托管，优化托管启动脚本支持 Ubuntu操作系统 （通过skynet.shell.command属性配置，缺省：/bin/bash）
	- xmanager 自定义首页 https页面支持弹出新窗口
	- xmanager 服务管理页面，增加服务器拖拽排序。
	- xmanager 服务定义增加系统编码（pluginCode），方便应用系统的快速定位；应用系统、服务列表增加拖拽排序。
	- xmanager 服务定义移除 Flylog 配置项

# 2.1.1009.3.RELEASE

> 发布时间：2019年11月18日

bug修复
  * 解决stream.py脚本100%CPU占用问题
  * 解决ant-xagent.sh/ant-xmanager.sh进程检测的缺陷（实际服务不存在但是检测出服务在运行的结果）
  * NUMA平台自动识别自动绑定CPU内存到节点

# 2.1.1009.2.RELEASE

> 发布时间：2019年10月14日

bug修复：
* 修复stop.sh无法kill整个进程子树的问题
* 修复sigar引发的UnstatisfiedLinkError，导致xagent启动失败的问题
* 修复python3环境下调用报错导致无法获取标准输出的问题

# 2.1.1009.1.RELEASE

> 发布时间：2019年09月24日

高危漏洞修复:   
Fastjson版本升级到1.2.61版本

流式计算：
节点数据统计：统计计数器进行了容错优化，不会因为中间一次的采集数据丢失而导致前后的数据对不上。

服务托管：
服务托管模式优化，xagent与被托管进程进程关系解耦，xagent被kill不影响被托管进程，xagent重启后能继续获取目标服务的标准输出。

xagent 内存占用优化，默认最大堆内存512MB。

bug 修复：
* xagent内存泄露：在GPU的服务器上开启性能采集场景下，频繁启动托管目标服务造成内存泄漏。
* 启动脚本优化:
* 使用pid文件控制启停
* 集群监控后台优化：
* 使用缓存提高页面访问速度
* 添加若干第三方开源组件监控：
* 添加redis/mysql/mongodb/es/rabbitmq的监控
* 元数据获取优化：
* 增加构建号和构建分支信息，提高问题追溯能力


# 2.1.1009.RELEASE

> 发布时间：2019年07月30日16:33:49

1、流式计算：

* 节点数据统计：增加每个节点数据流动统计和计算流程可视化展示。（详细见 http://wiki.iflytek.com/pages/viewpage.action?pageId=248974139）
* 集成Zipkin进行流程数据链路跟踪监控；
* 增加 @SkynetMqSvcContext @SkynetMqSvcHandler @SkynetProcessBuilder @SkynetProcessDispatchContext 注解，简化消息处理器和流程定义的注解的复杂性。
OnlineMQReport增加与zk掉线的补偿机制。

2、服务托管：

* 支持三级属性配置KEY作为占位符引用与替换；初始化工作目录占位符；
* JavaBoot支持skywalking 进行监控与服务,方便服务的链路跟踪和性能分析；
* WebAPI 管理接口添加 在线服务状态查询；
* 增加 带标签Metrics收集器 SkynetMetricsService，使用方式详细见(http://wiki.iflytek.com/pages/viewpage.action?pageId=244387897 * SkynetMetricsService)；
* 增加 SkynetMetrics gauge指标自动采集，主要针对ObjectPool<?>. 使用方式详细见(http://wiki.iflytek.com/pages/viewpage.action?pageId=244387897 * SkynetMetricsService)
* 支持 springboot 1.x 和 2.x 的托管；
* 添加本地配置文件占位符：${SKYNET_CONFIG_LOCATION_FILE} 如：/iflytek/server/skynet/tmp/<EMAIL>；
* xmanager和xagent 服务停止时主动从服务注册中心下线（kill -15 pid)；
* dashboard报表定义的自动导入。（自动导入 将放在 服务的工作目录和对应的仓库目录下的 dashboard*.json 到 Grafana）；
* 增加缺省jdk1.8.0_261运行环境包。（意味目标部署环境可以不用安装JAVA运行环境）；
* 移除服务管理7230端口；
* 增加xManager查看入口；
* 日志配置使用SpringBoot规范，如 logging.pattern.console,logging.pattern.file等；
* 日志查看页面字体样式优化；
* xMnager集群管理页面，状态获取优化排除跨域问题、服务节点右键菜单整理优化等。

3、系统监控与预警：

* 采集方案调整：将以前定时日志主动采集上报 改为 Prometheus 拉取模式，
* 优势：
* 数据存储体积减小（一个指标2字节存储空间）
* 同时充分利用业界成熟的Prometheus Exporter进行组件数据的采集。
* 增加自定义采集指标端点。
* 监控图表优化：增加cpu占用、内存占用的TopN排序显示，GPU、CPU温度等。
* 图表组件版本升级：Grafana 6.2.4
* 增加预警模块，支持 监控模板、计算指标的自定义，预警通道支持：微信、邮件、* Webhook。

4、配置管理：

三级配置中支持 占位符属性名替换，如：mysql.ip=${xx.ip}

5、bug 修复：

serverSession内存泄露（会话没有设置超时）

6、其他改进：

支持国产化ARM架构服务器。（重点 sigar 在arm上的适配 ）


# 2.1.1008

> 发布时间：2019年04月22日17:51:28

1、流式计算：   
* Kafka消费者线程池添加threadName，以及消费者线程丢失（如jvm内存紧张时）后的自动补* 偿功能完善；
* 消息生产者增加了消息分组TopicKey的设置；（方便同一业务的消息发送到同一topic，保证* 整体有序）；

2、服务托管：   
* 对关联文件为空时，兼用性优化；
* Java服务（SpringBoot|SkynetBoot）增加 JavaOpts 缺省 jvm参数设置（-Xms128M * -Xmx2G）；
* 增加服务注册中心在线服务状态异常下线的补偿注册机制；
* 优化服务治理的 WebAPI 接口。详细说明可参考SkynetWiki 服务治理-管理API;
* DockerBoot 启动优化：未安装情况下提示；
* 服务器注册：并发注册与推送控制处理；
* 对skynet基础包并发部署做了控制和优化，同时加快了前台进度推送。

3、系统监控：  
优化监控图表，包括集群、主机、服务状态；
增加 Prometheus 数据采集配置集成，(访问地址： http://{ip:port}/prometheus)

4、基础服务：   
日志docker部署包优化，如ES服务支持日志的TTL设置；

5、配置管理：  
三级配置支持 SpringCloudConfig规范，xAgent同是SpringCloudServer，详细说明可参考SkynetWiki 4.配置服务

6、服务定义：   
SpringBoot托管FatJar支持文件通配符；docker服务定义将目录映射参数合并到docker参数中

7、服务发现：   
支持技术中心API网关 的服务策略节点地址的自动注册与删除；

8、管理后台：   
*对Xmanager管理界面进行了页面风格修改；
*增加登录首页背景和系统标题自定义；
*优化xmanger集群管理页面的服务状态获取速度（特别是在集群中有服务器未启动的情况下）

9、流式计算
配置兼用性优化（在多个MqParam Bean存在的情况下冲突）

10、代码优化与bug修复：
* 线程池 线程添加 线程名称
* 修复Log日志记录是使用了为格式化的日志串
* 修复grafana dashboard/datasource自动加载的相关bug
* 解决docker下的/dev/shm操作权限的问题，让路径其可配置(skynet.dev.shm.path系统属* 性)

# 2.1.1007

> 发布时间：2019年01月24日20:03:00

1、流式计算框架，独立调度服务，解决 实际流程节点 与 流程定义逻辑 在运行时的隔离。
2、服务托管：支持docker化服务。
3、基础服务组件Docker化 ：ELK、Kafka、Grafana等减轻部署复杂度。

# 2.1.1006

> 发布时间：2018年12月19日09:56:42

1、集成Flylog、Grafana。
2、健康检测优化：PID、TCP、HTTP
3、上线成功标志修改：健康检测成功时
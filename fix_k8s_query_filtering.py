#!/usr/bin/env python3
"""
修复 K8sQuery 过滤问题
为所有服务类添加正确的 fieldSelector 和 labelSelector 过滤
"""

import os
import re
import glob

def fix_k8s_query_filtering_in_file(file_path):
    """修复单个文件中的 K8sQuery 过滤"""
    print(f"正在修复 K8sQuery 过滤: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 1. 修复 K8sNodeServiceImpl - 添加 fieldSelector 和 labelSelector 过滤
    if 'K8sNodeServiceImpl' in file_path:
        # 为 getNodes 方法添加过滤
        pattern = r'(List<JSONObject> nodeDtoList = new ArrayList<>\(\);\s*V1NodeList nodeList = api\.listNode\(\)\.execute\(\);\s*for \(V1Node node : nodeList\.getItems\(\)\) \{\s*nodeDtoList\.add\(toJSON\(node\)\);\s*\}\s*return nodeDtoList;)'
        replacement = '''List<JSONObject> nodeDtoList = new ArrayList<>();
        V1NodeList nodeList = api.listNode().execute();
        
        // 应用 K8sQuery 过滤条件
        List<V1Node> filteredNodes = applyK8sQueryFilter(nodeList.getItems(), k8sQuery);
        
        for (V1Node node : filteredNodes) {
            nodeDtoList.add(toJSON(node));
        }
        return nodeDtoList;'''
        content = re.sub(pattern, replacement, content, flags=re.DOTALL)
    
    # 2. 为其他服务类添加过滤
    else:
        # 匹配模式：for (ResourceType resource : resourceList.getItems()) {
        # 替换为：List<ResourceType> filteredResources = applyK8sQueryFilter(resourceList.getItems(), k8sQuery);
        #         for (ResourceType resource : filteredResources) {
        
        # Pod 过滤
        if 'K8sPodServiceImpl' in file_path:
            pattern = r'(for \(V1Pod pod : podList\.getItems\(\)\) \{)'
            replacement = '''// 应用 K8sQuery 过滤条件
        List<V1Pod> filteredPods = applyK8sQueryFilter(podList.getItems(), k8sQuery);
        
        for (V1Pod pod : filteredPods) {'''
            content = re.sub(pattern, replacement, content)
        
        # Deployment 过滤
        elif 'K8sDeploymentServiceImpl' in file_path:
            pattern = r'(for \(V1Deployment deployment : deploymentList\.getItems\(\)\) \{)'
            replacement = '''// 应用 K8sQuery 过滤条件
        List<V1Deployment> filteredDeployments = applyK8sQueryFilter(deploymentList.getItems(), k8sQuery);
        
        for (V1Deployment deployment : filteredDeployments) {'''
            content = re.sub(pattern, replacement, content)
        
        # DaemonSet 过滤
        elif 'K8sDaemonSetServiceImpl' in file_path:
            pattern = r'(for \(V1DaemonSet daemonSet : daemonSetList\.getItems\(\)\) \{)'
            replacement = '''// 应用 K8sQuery 过滤条件
        List<V1DaemonSet> filteredDaemonSets = applyK8sQueryFilter(daemonSetList.getItems(), k8sQuery);
        
        for (V1DaemonSet daemonSet : filteredDaemonSets) {'''
            content = re.sub(pattern, replacement, content)
        
        # StatefulSet 过滤
        elif 'K8sStatefulSetServiceImpl' in file_path:
            pattern = r'(for \(V1StatefulSet statefulSet : statefulSetList\.getItems\(\)\) \{)'
            replacement = '''// 应用 K8sQuery 过滤条件
        List<V1StatefulSet> filteredStatefulSets = applyK8sQueryFilter(statefulSetList.getItems(), k8sQuery);
        
        for (V1StatefulSet statefulSet : filteredStatefulSets) {'''
            content = re.sub(pattern, replacement, content)
        
        # Job 过滤
        elif 'K8sJobServiceImpl' in file_path:
            pattern = r'(for \(V1Job job : jobList\.getItems\(\)\) \{)'
            replacement = '''// 应用 K8sQuery 过滤条件
        List<V1Job> filteredJobs = applyK8sQueryFilter(jobList.getItems(), k8sQuery);
        
        for (V1Job job : filteredJobs) {'''
            content = re.sub(pattern, replacement, content)
        
        # CronJob 过滤
        elif 'K8sCronJobServiceImpl' in file_path:
            pattern = r'(for \(V1CronJob cronJob : cronJobList\.getItems\(\)\) \{)'
            replacement = '''// 应用 K8sQuery 过滤条件
        List<V1CronJob> filteredCronJobs = applyK8sQueryFilter(cronJobList.getItems(), k8sQuery);
        
        for (V1CronJob cronJob : filteredCronJobs) {'''
            content = re.sub(pattern, replacement, content)
        
        # ReplicaSet 过滤
        elif 'K8sReplicaSetServiceImpl' in file_path:
            pattern = r'(for \(V1ReplicaSet replicaSet : replicaSetList\.getItems\(\)\) \{)'
            replacement = '''// 应用 K8sQuery 过滤条件
        List<V1ReplicaSet> filteredReplicaSets = applyK8sQueryFilter(replicaSetList.getItems(), k8sQuery);
        
        for (V1ReplicaSet replicaSet : filteredReplicaSets) {'''
            content = re.sub(pattern, replacement, content)
        
        # Service 过滤
        elif 'K8sServiceServiceImpl' in file_path:
            pattern = r'(for \(V1Service service : serviceList\.getItems\(\)\) \{)'
            replacement = '''// 应用 K8sQuery 过滤条件
        List<V1Service> filteredServices = applyK8sQueryFilter(serviceList.getItems(), k8sQuery);
        
        for (V1Service service : filteredServices) {'''
            content = re.sub(pattern, replacement, content)
        
        # ConfigMap 过滤
        elif 'K8sConfigMapServiceImpl' in file_path:
            pattern = r'(for \(V1ConfigMap configMap : configMapList\.getItems\(\)\) \{)'
            replacement = '''// 应用 K8sQuery 过滤条件
        List<V1ConfigMap> filteredConfigMaps = applyK8sQueryFilter(configMapList.getItems(), k8sQuery);
        
        for (V1ConfigMap configMap : filteredConfigMaps) {'''
            content = re.sub(pattern, replacement, content)
        
        # Secret 过滤
        elif 'K8sSecretServiceImpl' in file_path:
            pattern = r'(for \(V1Secret secret : secretList\.getItems\(\)\) \{)'
            replacement = '''// 应用 K8sQuery 过滤条件
        List<V1Secret> filteredSecrets = applyK8sQueryFilter(secretList.getItems(), k8sQuery);
        
        for (V1Secret secret : filteredSecrets) {'''
            content = re.sub(pattern, replacement, content)
        
        # Endpoints 过滤
        elif 'K8sEndpointServiceImpl' in file_path:
            pattern = r'(for \(V1Endpoints endpoint : endpointList\.getItems\(\)\) \{)'
            replacement = '''// 应用 K8sQuery 过滤条件
        List<V1Endpoints> filteredEndpoints = applyK8sQueryFilter(endpointList.getItems(), k8sQuery);
        
        for (V1Endpoints endpoint : filteredEndpoints) {'''
            content = re.sub(pattern, replacement, content)
        
        # Event 过滤
        elif 'K8sEventServiceImpl' in file_path:
            pattern = r'(for \(CoreV1Event event : eventList\.getItems\(\)\) \{)'
            replacement = '''// 应用 K8sQuery 过滤条件
        List<CoreV1Event> filteredEvents = applyK8sQueryFilter(eventList.getItems(), k8sQuery);
        
        for (CoreV1Event event : filteredEvents) {'''
            content = re.sub(pattern, replacement, content)
        
        # Ingress 过滤
        elif 'K8sIngressServiceImpl' in file_path:
            pattern = r'(for \(V1Ingress ingress : ingressList\.getItems\(\)\) \{)'
            replacement = '''// 应用 K8sQuery 过滤条件
        List<V1Ingress> filteredIngresses = applyK8sQueryFilter(ingressList.getItems(), k8sQuery);
        
        for (V1Ingress ingress : filteredIngresses) {'''
            content = re.sub(pattern, replacement, content)
        
        # HPA 过滤
        elif 'K8sHorizontalPodAutoscalerServiceImpl' in file_path:
            pattern = r'(for \(V2HorizontalPodAutoscaler hpa : hpaList\.getItems\(\)\) \{)'
            replacement = '''// 应用 K8sQuery 过滤条件
        List<V2HorizontalPodAutoscaler> filteredHPAs = applyK8sQueryFilter(hpaList.getItems(), k8sQuery);
        
        for (V2HorizontalPodAutoscaler hpa : filteredHPAs) {'''
            content = re.sub(pattern, replacement, content)
        
        # CRD 过滤 (CRD 是集群级别资源，不支持 namespace 过滤，但支持 fieldSelector 和 labelSelector)
        elif 'K8sCustomResourceDefinitionServiceImpl' in file_path:
            pattern = r'(for \(V1CustomResourceDefinition crd : crdList\.getItems\(\)\) \{)'
            replacement = '''// 应用 K8sQuery 过滤条件 (CRD 是集群级别资源，只支持 fieldSelector 和 labelSelector)
        List<V1CustomResourceDefinition> filteredCRDs = applyK8sQueryFilter(crdList.getItems(), k8sQuery);
        
        for (V1CustomResourceDefinition crd : filteredCRDs) {'''
            content = re.sub(pattern, replacement, content)
    
    # 保存修改后的文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✓ 已修复 K8sQuery 过滤 {file_path}")
        return True
    else:
        print(f"  - 无需修改 {file_path}")
        return False

def main():
    """主函数"""
    # 查找所有需要修复的 Java 文件
    java_files = glob.glob("skynet-platform-xmanager/src/main/java/skynet/platform/manager/k8s/service/impl/*ServiceImpl.java")
    
    # 排除不需要修复的文件
    exclude_files = ['K8sBaseService.java', 'K8sWorkloadService.java', 'K8sKubectlService.java']
    java_files = [f for f in java_files if not any(exclude in f for exclude in exclude_files)]
    
    fixed_count = 0
    total_count = len(java_files)
    
    print(f"找到 {total_count} 个 Java 文件需要修复 K8sQuery 过滤...")
    
    for java_file in java_files:
        if fix_k8s_query_filtering_in_file(java_file):
            fixed_count += 1
    
    print(f"\nK8sQuery 过滤修复完成！共修复了 {fixed_count}/{total_count} 个文件。")

if __name__ == "__main__":
    main()

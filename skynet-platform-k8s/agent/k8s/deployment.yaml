apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: skynet-agent
  name: skynet-agent
  namespace: skynet-agent-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: skynet-agent
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: skynet-agent
    spec:
      containers:
      - image: sealos.hub:5000/skynet/agent:1052
        name: skynet-agent
        resources: {}
        volumeMounts:
          - name: skynet-agent-config
            mountPath: /agent.properties
            subPath: agent.properties
      volumes:
        - name: skynet-agent-config
          configMap:
            name: skynet-agent-config
      serviceAccountName: skynet-agent
status: {}

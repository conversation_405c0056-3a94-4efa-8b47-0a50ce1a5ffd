package skynet

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	log "github.com/cihub/seelog"
)

const (
	SKYNET_TOKEN_URL             = "%s/skynet/auth/token"
	SKYNET_ACTION_DEFINITION_URL = "%s/skynet/api/v3/k8s/action/definition"
	SKYNET_ACTION_RESOURCE_URL   = "%s/skynet/api/v3/k8s/action/resource"
)

type ClientProperties struct {
	ManagerUrl  string `mapstructure:"skynet_manager_url"`
	ApiKey      string `mapstructure:"skynet_auth_api-key"`
	ApiSecret   string `mapstructure:"skynet_auth_api-secret"`
	ActionPoint string `mapstructure:"skynet_action-point"`
	Index       int    `mapstructure:"skynet_index"`
	IP          string `mapstructure:"skynet_action_ip"`
}

type SkynetClient struct {
	clientProperties ClientProperties
}

func NewClient(clientProperties ClientProperties) *SkynetClient {
	return &SkynetClient{
		clientProperties: clientProperties,
	}
}

// get the action definition
func (client *SkynetClient) FetchDefinition(port int, ports []int) ActionDefinitionDto {

	k8sDependResourceRequest := K8sDependResourceRequest{
		ActionPoint: client.clientProperties.ActionPoint,
		Index:       client.clientProperties.Index,
		IP:          client.clientProperties.IP,
		Port:        port,
		Ports:       ports,
	}
	targetUrl := fmt.Sprintf(SKYNET_ACTION_DEFINITION_URL, client.clientProperties.ManagerUrl)
	response := post[ActionDefinitionDto](client.clientProperties, targetUrl, k8sDependResourceRequest)
	return response
}

// execute a post request to skynet xmanager
func post[D interface{}](clientProperties ClientProperties, targetUrl string, data interface{}) D {

	jsonData, err := json.Marshal(data)
	if err != nil {
		log.Error(err)
	}

	var DefaultClient = &http.Client{}
	req, err := http.NewRequest("POST", targetUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		log.Error(err)
	}

	token := getToken(clientProperties.ManagerUrl, clientProperties.ApiKey, clientProperties.ApiSecret)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("skynet_token", token)

	resp, err := DefaultClient.Do(req)
	if err != nil {
		log.Error(err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		panic("read response data error," + err.Error())
	}
	if resp.StatusCode != 200 {
		panic(string(body))
	}

	log.Debug(string(body))

	var skynetApiResponse SkynetApiResponse[D]
	json.Unmarshal(body, &skynetApiResponse)
	if skynetApiResponse.Code != 0 {
		log.Error("request %s error: %s", targetUrl, skynetApiResponse.Message)
		panic("request %s error," + skynetApiResponse.Message)
	}

	log.Debug("skynetApiResponse data=", skynetApiResponse.Data)
	return skynetApiResponse.Data
}

// get a skynet token
func getToken(skynetHost, apiKey, apiSecret string) string {

	tokenUrl := fmt.Sprintf(SKYNET_TOKEN_URL, skynetHost)
	tokenUrl = AssembleRequestUrl(tokenUrl, "get", apiKey, apiSecret)

	log.Info(tokenUrl)
	resp, err := http.Get(tokenUrl)
	if err != nil {
		log.Error(err)
		panic("request skynet token error," + err.Error())
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		panic("read skynet token data error," + err.Error())
	}
	log.Debug(string(body))
	var skynetApiResponse SkynetApiResponse[*AccessToken]
	json.Unmarshal(body, &skynetApiResponse)
	if skynetApiResponse.Code != 0 {
		log.Error("get skynet token error: %s", skynetApiResponse.Message)
		panic("get skynet token error," + skynetApiResponse.Message)
	}

	log.Debug("skynet_token=", skynetApiResponse.Data.Token)
	return skynetApiResponse.Data.Token
}

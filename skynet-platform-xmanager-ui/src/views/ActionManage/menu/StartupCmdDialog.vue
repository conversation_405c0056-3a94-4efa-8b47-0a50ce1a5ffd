<template>
  <!-- :modal-append-to-body="false" -->
  <el-dialog
    :visible.sync="dialogVisible"
    :title="$t('506')"
    width="900px"
    top="30vh"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    @close="onDialogClose"
    :append-to-body="true"
  >
    <div class="startup-cmd">
      <div class="title">
        <span>{{ $t('507') }}</span>
        <span>
          <a href="javascript:void(0)" @click="onViewStdoutLog">{{ $t('508') }}</a>
          <a href="javascript:void(0)" @click="onCopy">{{ $t('509') }}</a>
          <a href="javascript:void(0)" @click="onViewResponse">{{ $t('510') }}</a>
        </span>
      </div>
      <div class="content" v-loading="loading">
        <!-- v-html="cmdHTML" -->
        <prism-editor :readonly="true" class="my-prism-editor" v-model="cmdHTML" :highlight="highlighter" line-numbers></prism-editor>
      </div>
      <div class="title" v-show="failedReason">
        <span>{{ $t('511') }}</span>
      </div>
      <div class="content" v-show="failedReason">
        <!-- v-html="failedReason" -->
        <prism-editor :readonly="true" class="my-prism-editor" v-model="failedReason" :highlight="highlighter" line-numbers></prism-editor>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import locale from '@/i18n/index.js'

import runtime from '@/axios/api/runtime'
import { copyToClip } from '@/common/util'
import menuHandler from './menu-handler'
import { highlight, languages } from 'prismjs/components/prism-core'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-bash'
import 'prismjs/themes/prism.css'
import MD5 from 'md5.js'
// import router from '@/router'

export default {
  name: 'StartupCmdDialog',
  data() {
    return {
      badgeEvent: null,
      dialogVisible: false,
      loading: false,
      response: '',
      cmd: '',
      cmdHTML: '',
      failedReason: ''
    }
  },
  methods: {
    highlighter(data) {
      return highlight(data, languages.bash) // languages.<insert language> to return html with markup
    },
    open(badgeEvent) {
      this.badgeEvent = badgeEvent
      this.dialogVisible = true
      this.loading = true
      const __this = this
      runtime
        .getBootStatus(badgeEvent.ip, badgeEvent.agentPort, badgeEvent.actionID)
        .then(d => {
          let json = typeof d === 'string' ? JSON.parse(d) : d
          __this.response = JSON.stringify(json, null, 2)
          __this.cmd = json.cmd
          // __this.cmdHTML = (json.cmd) ? json.cmd.replace(/\r\n/g, '</br>').replace(/\n/g, '</br>') : '(无)'
          __this.cmdHTML = json.cmd
          if (json.status && json.status.failed) {
            // __this.failedReason = json.status.failed.replace(/\r\n/g, '</br>').replace(/\n/g, '</br>')
            __this.failedReason = json.status.failed
          }
        })
        .catch(err => {
          console.error(`fail to get action definition of ${badgeEvent.actionID}`, err)
          __this.$message({
            type: 'error',
            message: locale.t('512')
          })
          __this.dialogVisible = false
        })
        .finally(() => {
          __this.loading = false
        })
    },
    onDialogClose() {
      this.response = ''
      this.cmd = ''
      this.cmdHTML = ''
      this.failedReason = ''
      this.loading = false
    },
    onCopy() {
      copyToClip(this, this.cmd)
    },
    onViewResponse() {
      let badgeEvent = this.badgeEvent
      let url = runtime.getBootStatusUrl(badgeEvent.ip, badgeEvent.agentPort, badgeEvent.actionID)
      let md5sum = new MD5().update(url).digest('hex')
      let title = encodeURIComponent(badgeEvent.actionName)
      window.sessionStorage.setItem(md5sum, url)
      window.open(`/#/fetcher/${md5sum}?title=${title}`)
    },
    onViewStdoutLog() {
      this.dialogVisible = false
      menuHandler.showStdoutLog(this.badgeEvent)
    }
  }
}
</script>
<style scoped lang="scss">
// @import 'prismjs/themes/prism-tomorrow.css';
</style>
<style lang="scss">
.startup-cmd {
  max-height: 50vh;
  overflow-y: auto;
  // padding-bottom: 10px;
  .title {
    padding-right: 15px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    a + a {
      margin-left: 15px;
    }
    a {
      color: rgb(59, 116, 240);
      font-size: 14px;
      &:hover {
        color: rgba(59, 116, 240, 0.6);
      }
    }
    span {
      font-size: 16px;
      font-weight: bold;
    }
  }
  .content {
    padding: 15px;
    font-size: 14px;
    line-height: 1.4;
    word-break: break-all;
  }
}
</style>

<template>
  <div class="sky-table-contanier configmaps-contanier">
    <div class="toolbar">
      <namespace-select @selectChange="onNamespaceSelectChange" :ip="ip" v-model="namespace" />&nbsp;
      <el-input
        style="width:25%;"
        :placeholder="$t('638')"
        suffix-icon="el-icon-search"
        v-model="filterKeyword"
        @clear="filterKeyword = null"
        clearable
      />
      <span class="right">
        <el-button type="primary" icon="el-icon-refresh" @click="onRefresh">{{ $t('147') }}</el-button>
        <el-button type="primary" plain icon="el-icon-plus" @click="onCreate">{{ $t('639') }}</el-button>
      </span>
    </div>
    <div class="body">
      <el-table :data="viewList" header-row-class-name="headbg" :height="_setTHeight(120)" v-loading="loading" :element-loading-text="$t('628')">
        <el-table-column align="center" type="index" :label="$t('228')" width="50"> </el-table-column>
        <el-table-column align="left" :label="$t('640')" prop="metadata.name" min-width="120" sortable show-overflow-tooltip>
          <template slot-scope="scope">
            <el-link type="primary" @click="onObjectDetail(scope.row)">
              {{ scope.row.metadata.name }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('641')" width="120" prop="metadata.namespace" sortable show-overflow-tooltip />
        <el-table-column align="center" :label="$t('231')" prop="spec.type" sortable show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.spec.type }}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" :label="$t('916')" prop="spec.clusterIP" sortable show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.spec.clusterIP }}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" :label="$t('233')">
          <template slot-scope="scope">
            <span v-if="scope.row.spec.type == 'ClusterIP'">
              <el-tag v-for="(port, i) in scope.row.spec.ports" :key="i"> {{ port.port }} | {{ port.protocol }} </el-tag>
            </span>
            <span v-if="scope.row.spec.type == 'NodePort'">
              <el-tag v-for="(port, i) in scope.row.spec.ports" :key="i"> {{ port.port }} | {{ port.nodePort }} | {{ port.protocol }} </el-tag>
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('644')" width="100" prop="metadata.creationAt" sortable show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.metadata.creationAt" placement="top-start">
              <span> {{ scope.row.metadata.creationAge }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('239')" width="120">
          <template slot-scope="scope">
            <el-dropdown split-button plain size="mini" icon="el-icon-view" @command="handleCommand($event, scope.row)" @click="onYAML(scope.row)">
              <span>{{ $t('645') }}</span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item icon="el-icon-delete" command="delete">{{ $t('65') }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <yaml-dialog
      :ip="ip"
      :value="yamlContent"
      :title="yamlTitle"
      :create="yamlCreate"
      v-if="yamlShowDialog"
      @close="yamlShowDialog = false"
      @refresh="onRefresh"
    />
    <confirm-dialog ref="confirmDialogRef" :isDragVerify="true" />
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import NamespaceSelect from '@/views/AgentManage/kubernetes/components/namespace-select/index.vue'
import YamlDialog from '@/views/AgentManage/kubernetes/components/yaml-dialog/index.vue'
import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
import k8sUtil from '@/utils/k8s/com'
import { SERVICE_TEMPLATE } from '@/utils/k8s/yaml'
export default {
  name: 'Services',
  components: {
    NamespaceSelect,
    YamlDialog,
    ConfirmDialog
  },
  data() {
    return {
      loading: false,
      namespace: '',
      filterKeyword: null,
      dataList: [],
      viewList: [],
      yamlTitle: '',
      yamlContent: '',
      yamlShowDialog: false,
      yamlCreate: false
    }
  },
  created() {
    this.onRefresh()
  },
  computed: {
    ip() {
      return this.$route.params.ip
    }
  },
  watch: {
    filterKeyword: {
      handler: function(val, oldVal) {
        this.filterList(val)
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      this.loading = true
      this.$api.kubernetes
        .getServices(this.ip)
        .then(res => {
          this.dataList = k8sUtil.parseK8sDataList(res)
          this.filterList(this.filterKeyword)
        })
        .finally(() => {
          this.loading = false
        })
    },
    filterList(filterKeyword) {
      var namespace = this.namespace
      this.viewList = filterKeyword ? this.dataList.filter(item => item.metadata.name.indexOf(filterKeyword) >= 0) : this.dataList
      this.viewList = namespace ? this.viewList.filter(item => item.metadata.namespace === namespace) : this.viewList
    },
    onNamespaceSelectChange(val) {
      this.namespace = val
      this.filterList(this.filterKeyword)
    },
    // 刷新按钮
    onRefresh(delay) {
      if (delay) {
        setTimeout(() => {
          this.initData()
        }, delay)
      } else {
        this.initData()
      }
    },
    onSelectDelete() {
      // TODO:
    },
    onObjectDetail(row) {
      // TODO:
    },
    onYAML(row) {
      this.loading = true
      this.$api.kubernetes
        .getServiceYaml(this.ip, row.metadata.namespace, row.metadata.name)
        .then(res => {
          this.yamlTitle = `${locale.t('917')}${row.metadata.name}`
          this.yamlContent = res
          this.yamlShowDialog = true
          this.yamlCreate = false
        })
        .finally(() => {
          this.loading = false
        })
    },
    onCreate() {
      this.yamlTitle = `${locale.t('647')}`
      this.yamlContent = SERVICE_TEMPLATE
      this.yamlShowDialog = true
      this.yamlCreate = true
    },
    onDelete(row) {
      let confirmCallback = () => {
        this.loadingText = locale.t('918')
        this.loading = true
        this.$api.kubernetes
          .deleteService(this.ip, row.metadata.namespace, row.metadata.name)
          .then(res => {
            this.$message.success(`${locale.t('919')}${row.metadata.name}${locale.t('650')}`)
            this.onRefresh(2000)
          })
          .finally(() => {
            this.loading = false
          })
      }
      let title = `${locale.t('920')}${row.metadata.name} ] ?`
      let names = []
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    },
    handleCommand(cmd, row) {
      if (cmd === 'delete') {
        this.onDelete(row)
      }
    }
  }
}
</script>
<style scoped lang="scss">
.service-manage-contanier {
  .el-tag {
    margin-right: 2px;
  }
}
</style>

<template>
  <div class="namespace-select">
    <span>{{ $t('629') }}</span>
    <el-select v-model="currentNamespace" :placeholder="$t('118')" @change="handleChange" clearable>
      <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
    </el-select>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

export default {
  data() {
    return {
      options: [
        {
          value: '',
          label: locale.t('630')
        }
      ],
      currentNamespace: ''
    }
  },
  watch: {
    $route: {
      handler: function() {
        if (this.namespace) {
          this.currentNamespace = this.namespace
        } else {
          let temp = window.sessionStorage.getItem('x-current-select-namespace')
          this.currentNamespace = temp || ''
        }
        this.$emit('selectChange', this.currentNamespace)
      },
      immediate: true,
      deep: true
    }
  },
  props: {
    namespace: String,
    ip: ''
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      this.loading = true
      this.$api.kubernetes
        .getNamespaces(this.ip)
        .then(res => {
          this.options = [
            ...this.options,
            ...res.map(x => {
              return {
                value: x.metadata.name,
                label: x.metadata.name
              }
            })
          ]
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleChange($event) {
      window.sessionStorage.setItem('x-current-select-namespace', $event)
      this.$emit('selectChange', $event)
    }
  }
}
</script>
<style lang="scss">
.namespace-select {
  float: left;
}
</style>

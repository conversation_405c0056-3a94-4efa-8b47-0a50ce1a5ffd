<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="onDialogClose"
    :title="$t('668')"
    v-loading="loading"
    :element-loading-text="$t('700')"
  >
    <div class="body">
      <el-form label-width="130px" :model="strategy">
        <el-form-item :label="$t('231')">
          <el-select v-model="strategy.type" :placeholder="$t('701')" style="width: 80%;">
            <el-option :label="$t('702')" value="OnDelete"></el-option>
            <el-option :label="$t('703')" value="RollingUpdate"></el-option>
          </el-select>
          <el-tooltip class="item" effect="light" placement="right">
            <div slot="content">
              <p>
                <b>{{ $t('704') }}</b>
              </p>
              <p>
                <b>{{ $t('702') }}</b
                >{{ $t('778') }}<br />{{ $t('779') }}<br />{{ $t('780') }}<br />{{ $t('781') }}<br />
              </p>
              <p>
                <b>{{ $t('703') }}</b>
              </p>
              <p>{{ $t('782') }}<br />{{ $t('783') }}<br />{{ $t('784') }}<br />{{ $t('785') }}<br />{{ $t('786') }}<br />{{ $t('787') }}<br /></p>
            </div>
            <i class="el-icon-question" />
          </el-tooltip>
        </el-form-item>
        <el-form-item :label="$t('788')" v-if="strategy.type === 'RollingUpdate'">
          <el-input-number v-model="strategy.rollingUpdate.partition" :min="0" :max="50" :label="$t('788')" style="width: 80%;"></el-input-number>
          <el-tooltip class="item" effect="light" placement="right">
            <div slot="content">
              <p>
                <b>{{ $t('788') }}</b>
              </p>
              <p>{{ $t('789') }}<br />{{ $t('790') }}<br />{{ $t('791') }}<br /></p>
            </div>
            <i class="el-icon-question" />
          </el-tooltip>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" icon="el-icon-check" type="primary" @click.stop="onConfirm">{{ $t('75') }}</el-button>
      <el-button size="mini" icon="el-icon-close" @click="dialogVisible = false">{{ $t('76') }}</el-button>
    </div>
  </el-dialog>
</template>
<script>
import locale from '@/i18n/index.js'

export default {
  name: 'StatefulsetStrategy',
  components: {},
  props: {
    callback: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      ip: '',
      statefulset: '',
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          partition: '0'
        }
      }
    }
  },
  computed: {},
  methods: {
    open(ip, statefulset) {
      this.ip = ip
      this.statefulset = statefulset
      if (statefulset && statefulset.spec && statefulset.spec.updateStrategy) {
        this.strategy.type = statefulset.spec.updateStrategy.type
        if (statefulset.spec.updateStrategy.rollingUpdate) {
          this.strategy.rollingUpdate.partition = statefulset.spec.updateStrategy.rollingUpdate.partition
        }
      }
      this.dialogVisible = true
    },
    onDialogClose() {
      this.dialogVisible = false
    },
    onConfirm() {
      if (!this.statefulset) {
        this.dialogVisible = false
        return
      }
      // TODO:
      this.loading = true
      this.$api.kubernetes
        .statefulsetStrategy(this.ip, this.statefulset.metadata.namespace, this.statefulset.metadata.name, {
          type: this.strategy.type,
          rollingUpdate: this.strategy.type === 'RollingUpdate' ? this.strategy.rollingUpdate : null
        })
        .then(res => {
          this.$message.success(`${locale.t('716')}`)
          this.dialogVisible = false
          if (this.callback) this.callback()
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>

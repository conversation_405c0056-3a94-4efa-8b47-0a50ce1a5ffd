<template>
  <div class="docker-part-probe">
    <div class="title">
      <el-tag size="mini">{{ title }}</el-tag>
      <el-tag size="mini" v-if="probeType">{{ probeType }}</el-tag>
    </div>
    <div class="detail" v-if="!probe">
      <el-tag type="danger" size="medium">{{ $t('826') }}</el-tag>
    </div>
    <div class="detail" v-else>
      <div class="main">
        <div class="field_kv">
          <div class="label">{{ probeType }}</div>
          <div class="value">{{ probeContent }}</div>
        </div>
      </div>
      <div class="frequency">
        <div class="field_kv">
          <div class="label">{{ $t('827') }}</div>
          <div class="value" v-if="probe.initialDelaySeconds">{{ probe.initialDelaySeconds }}{{ $t('828') }}</div>
          <div class="value" v-else>{{ $t('829') }}</div>
        </div>
        <div class="field_kv">
          <div class="label">{{ $t('830') }}</div>
          <div class="value" v-if="probe.periodSeconds">{{ probe.periodSeconds }}{{ $t('828') }}</div>
          <div class="value" v-else>{{ $t('829') }}</div>
        </div>
        <div class="field_kv">
          <div class="label">{{ $t('831') }}</div>
          <div class="value" v-if="probe.timeoutSeconds">{{ probe.timeoutSeconds }}{{ $t('828') }}</div>
          <div class="value" v-else>{{ $t('829') }}</div>
        </div>
        <div class="field_kv">
          <div class="label">{{ $t('832') }}</div>
          <div class="value" v-if="probe.successThreshold">{{ probe.successThreshold }}</div>
          <div class="value" v-else>{{ $t('829') }}</div>
        </div>
        <div class="field_kv">
          <div class="label">{{ $t('833') }}</div>
          <div class="value" v-if="probe.failureThreshold">{{ probe.failureThreshold }}</div>
          <div class="value" v-else>{{ $t('829') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

export default {
  props: {
    title: {
      type: String,
      rquired: true
    },
    probe: {
      type: Object,
      rquired: true
    }
  },
  computed: {
    probeType() {
      if (!this.probe) return null
      if (this.probe.exec) return 'exec'
      if (this.probe.grpc) return 'grpc'
      if (this.probe.httpGet) return 'httpGet'
      if (this.probe.tcpSocket) return 'tcpSocket'
    },
    probeContent() {
      if (!this.probe) return null
      if (this.probe.exec) return this.formatExec(this.probe.exec)
      if (this.probe.grpc) return this.formatGrpc(this.probe.grpc)
      if (this.probe.httpGet) return this.formatHttpGet(this.probe.httpGet)
      if (this.probe.tcpSocket) return this.formatTcpSocket(this.probe.tcpSocket)
    }
  },
  methods: {
    // exec:
    //   command:
    //     - cat
    //     - /tmp/healthy
    formatExec(exec) {
      return exec.command ? exec.command.join(' ') : ''
    },
    // grpc:
    //   port: 2379
    formatGrpc(grpc) {
      return `\${PodIP}:${grpc.port}`
    },
    // httpGet:
    //   path: /healthz
    //   port: 10254
    //   scheme: HTTP
    formatHttpGet(httpGet) {
      let scheme = httpGet.scheme ? httpGet.scheme.toLowerCase() : 'http'
      let port = httpGet.port ? ':' + httpGet.port : ''
      let path = httpGet.path ? httpGet.path : ''
      return `${scheme}://\${PodIP}${port}${path}`
    },
    // tcpSocket:
    //   port: 8080
    formatTcpSocket(tcpSocket) {
      return `\${PodIP}:${tcpSocket.port}`
    }
  }
}
</script>
<style scoped lang="scss">
.docker-part-probe {
  width: 50vw;
  max-width: 720px;
  max-height: calc(100vh - 200px);
  min-height: 80px;
  overflow: hidden;
  overflow-y: auto;

  background-color: #f1f4f8;
  border: 1px solid #ebeef5;
  border-radius: 3px;
  padding: 10px;
  margin-bottom: 10px;

  .detail {
    margin-top: 10px;
    .el-tag {
      width: 84px;
      text-align: center;
      height: 32px;
      padding: 0 10px;
      line-height: 30px;
    }

    .main {
      margin-bottom: 10px;
    }

    .frequency {
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
      border-top: 1px dashed #bdc8da;
      padding-top: 8px;
    }

    .field_kv,
    .frequency {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
    }

    .label,
    .value {
      display: inline-block;
      padding: 0;
      font-family: Consolas, Menlo, Bitstream Vera Sans Mono, Monaco, monospace;
      font-size: 12px;
      line-height: 20px;
    }

    .label {
      width: 70px;
      color: #909399;
      vertical-align: top;
    }

    .value {
      -webkit-box-flex: 1;
      -ms-flex-positive: 1;
      flex-grow: 1;
      width: 68px;
      color: #222;
      font-weight: 700;
    }
  }
}
</style>

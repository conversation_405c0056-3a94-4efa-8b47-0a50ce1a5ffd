<template>
  <section-container class="kv-config" :title="title" :isCollapsable="isCollapsable" :initialCollapsed="initialCollapsed">
    <div class="left" v-loading="loading" :element-loading-text="$t('213')">
      <div :class="{ text: true, 'no-data': !data }">
        <prism-editor
          class="my-prism-editor"
          :readonly="!edit"
          v-model="lines"
          :highlight="highlighter"
          line-numbers
        ></prism-editor>
      </div>
    </div>
    <div class="right" v-if="showOperations">
      <div v-if="edit" class="buttons">
        <div class="button">
          <el-button size="mini" type="primary" @click="submit">{{ $t('88') }}</el-button>
        </div>
        <div class="button">
          <el-button size="mini" plain @click="cancel">{{ $t('76') }}</el-button>
        </div>
        <div class="button">
          <el-button size="mini" plain @click="openToolDialog">{{ $t('259') }}</el-button>
        </div>
      </div>
      <div v-else class="links">
        <div class="link-type">
          <a href="javascript:void(0)" @click="edit = true">{{ $t('64') }}</a>
        </div>
        <div class="link-type">
          <a href="javascript:void(0)" @click="refresh">{{ $t('147') }}</a>
        </div>
        <div class="link-type">
          <a href="javascript:void(0)" @click="openToolDialog">{{ $t('259') }}</a>
        </div>
      </div>
    </div>
    <div class="clear" />
    <tool ref="toolDialogRef"></tool>
  </section-container>
</template>
<script>
import locale from '@/i18n/index.js'

import SectionContainer from '@/components/Container/SectionContainer'
import ToolDialog from '../../Tool/ToolDialog'
import { highlight, languages } from 'prismjs/components/prism-core'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-properties'
import 'prismjs/themes/prism.css'
export default {
  name: 'KVConfig',
  components: {
    'section-container': SectionContainer,
    tool: ToolDialog
  },
  props: {
    title: {
      type: String,
      required: true
    },
    showOperations: {
      type: Boolean,
      default: true
    },
    isCollapsable: {
      type: Boolean,
      default: true
    },
    initialCollapsed: {
      type: Boolean,
      default: null
    },
    submitFunc: {
      type: Function
    },
    refreshFunc: {
      type: Function
    }
  },
  data() {
    return {
      edit: false,
      loading: false,
      data: null,
      lines: '',
      code: '#ssss' + '\n' + 'sdfg=sdfg' + '\n' + '##########################' + '\n' + 'asdfgh=sdfghjhgfds'
    }
  },
  created() {
    this.lines = this.filterLastLine(this.data)
  },
  computed: {},
  watch: {
    data(val, oldval) {
      this.lines = this.filterLastLine(val)
    }
  },
  methods: {
    highlighter(data) {
      return highlight(data, languages.properties) // languages.<insert language> to return html with markup
    },
    refresh() {
      this.loading = true
      const __this = this
      this.refreshFunc()
        .then(d => {
          __this.data = d
        })
        .finally(() => {
          __this.loading = false
        })
    },
    submit() {
      const __this = this
      this.submitFunc(this.lines)
        .then(() => {
          __this.data = __this.lines.trim()
          __this.edit = false
        })
        .catch(() => {
          __this.cancel()
        })
    },
    cancel() {
      this.lines = this.filterLastLine(this.data)
      this.edit = false
    },
    openToolDialog() {
      this.$refs.toolDialogRef.open()
    },
    filterLastLine(data) {
      let ret = data || ''
      // 去除最后一个\n（后台提供数据的缺陷)
      if (ret.length > 0 && ret[ret.length - 1] === '\n') {
        ret = ret.substring(0, ret.length - 1)
      }
      return ret
    }
  }
}
</script>
<style scoped lang="scss">
.kv-config {
  width: 100%;
  height: 100%;
  .left,
  .right {
    float: left;
    height: 100%;
  }
  .left {
    // padding: 10px;
    width: calc(100% - 80px);
    overflow: auto;
    & > div {
      height: 100%;
    }
    .text {
      min-height: 120px;
      // border: 1px dashed $border-color-dark;
      // padding: 5px 15px;
      color: #909399;
    }
  }

  .no-data {
    color: $border-color-dark;
  }
  .right {
    width: 80px;
    position: relative;
    height: 42px;
    .buttons,
    .links {
      position: absolute;
      right: 21px;
      top: 0;
    }
    .button + .button,
    .link-type + .link-type {
      margin-top: 10px;
    }
  }

  .line {
    height: 25px;
    line-height: 25px;
  }
}
</style>
<style lang="scss">
.kv-config {
  .el-textarea {
    height: 100%;
    .el-textarea__inner {
      height: 100%;
      min-height: 350px !important;
    }
  }

  .edit-mode {
    background-color: #efefef;
  }
}
</style>

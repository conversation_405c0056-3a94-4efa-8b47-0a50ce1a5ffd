<template>
  <el-dialog
    class="update-password-container"
    width="500px"
    top="30vh"
    :title="$t('1065')"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="onDialogClose"
  >
    <el-form :model="formData" :rules="rules" ref="form" label-width="100px" label-position="left" hide-required-asterisk>
      <el-form-item :label="$t('1063')" prop="user">
        <el-input v-model="formData.user" :placeholder="$t('1072')" prefix-icon="el-icon-user-solid" clearable />
      </el-form-item>
      <el-form-item :label="$t('1073')" prop="originPassword">
        <el-input v-model="formData.originPassword" :placeholder="$t('1074')" prefix-icon="el-icon-lock" show-password clearable />
      </el-form-item>
      <el-form-item :label="$t('1075')" prop="newPassword">
        <el-input v-model="formData.newPassword" :placeholder="$t('1076')" prefix-icon="el-icon-lock" show-password clearable />
      </el-form-item>
      <el-form-item :label="$t('1077')" prop="confirmNewPassword">
        <el-input v-model="formData.confirmNewPassword" :placeholder="$t('1078')" prefix-icon="el-icon-lock" show-password clearable />
      </el-form-item>
      <el-button type="primary" @click="onConfirm()" class="confirm-btn">{{ $t('1079') }}</el-button>
    </el-form>
  </el-dialog>
</template>
<script>
import locale from '@/i18n/index.js'

import { updatePwd } from '@/axios/api/auth'
import { encryptPassword } from '@/utils/auth'
const emptyData = {
  user: '',
  originPassword: '',
  newPassword: '',
  confirmNewPassword: ''
}
export default {
  name: 'AgentEditDialog',
  data() {
    const __this = this
    const validatePassword = function(rule, value, callback) {
      const regex = /^(?![A-Za-z0-9]+$)(?![a-z0-9\W]+$)(?![A-Za-z\W]+$)(?![A-Z0-9\W]+$)[a-zA-Z0-9\W]{8,}$/
      if (regex.test(value)) {
        callback()
      } else {
        callback(new Error(locale.t('1080')))
      }
    }
    const validateConfirmPassword = function(rule, value, callback) {
      if (value !== __this.formData.newPassword) {
        callback(new Error(locale.t('1081')))
      }
      callback()
    }
    const rules = {
      user: [
        {
          required: true,
          message: locale.t('1082')
        }
      ],
      originPassword: [
        {
          required: true,
          message: locale.t('1083')
        }
      ],
      newPassword: [
        {
          required: true,
          message: locale.t('1084')
        },
        {
          validator: validatePassword,
          trigger: 'blur'
        }
      ],
      confirmNewPassword: [
        {
          required: true,
          message: locale.t('1085')
        },
        {
          validator: validateConfirmPassword,
          trigger: 'blur'
        }
      ]
    }
    return {
      dialogVisible: false,
      formData: emptyData,
      rules
    }
  },
  methods: {
    open() {
      let emptyDup = {}
      Object.assign(emptyDup, emptyData)
      this.formData = emptyDup
      this.dialogVisible = true
    },
    onDialogClose() {
      this.$refs.form.resetFields()
    },
    onConfirm() {
      const __this = this
      this.$refs.form.validate(valid => {
        if (valid) {
          let password = encryptPassword(__this.formData.originPassword)
          let newpassword = encryptPassword(__this.formData.newPassword)
          updatePwd({
            username: __this.formData.user,
            password,
            newpassword
          })
            .then(() => {
              __this.dialogVisible = false
            })
            .catch(err => {
              let reason = err.message
              let msg = reason ? `${locale.t('1086')}${reason}` : locale.t('1087')
              __this.$message({
                type: 'error',
                message: msg
              })
            })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.confirm-btn {
  margin-top: 10px;
  width: 100%;
}
</style>
<style lang="scss">
.update-password-container {
  .el-form-item--mini {
    margin-bottom: 20px;
  }
  .el-form-item__error {
    margin-top: 3px;
  }
}
</style>

<template>
  <div class="tool-container">
    <el-tabs v-model="activeTabName" :lazy="true" tab-position="left">
      <el-tab-pane :label="$t('1128')" name="password"><password></password></el-tab-pane>
      <el-tab-pane :label="$t('1129')" name="base64"><base64></base64></el-tab-pane>
      <el-tab-pane :label="$t('1130')" name="url"><uri></uri></el-tab-pane>
      <el-tab-pane :label="$t('1131')" name="md5"><md5></md5></el-tab-pane>
      <el-tab-pane v-if="showJsonTool" :label="$t('1122')" name="json">
        <JsonEditor />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import Password from './Password'
import Base64 from './Base64'
import Uri from './URI'
import Md5 from './MD5'
import JsonEditor from './JsonEditor'
export default {
  name: 'Tool',
  props: {
    showJsonTool: {
      type: Boolean,
      default: true
    }
  },
  components: {
    Password,
    Base64,
    Uri,
    Md5,
    JsonEditor
  },
  data() {
    return {
      activeTabName: 'password'
    }
  }
}
</script>
<style lang="scss">
.tool-container {
  background-color: #ffffff;
  height: 100%;
  padding: 20px;

  .el-tabs--left {
    height: 100%;
  }
  .el-tabs__item.is-left {
    text-align: left;
    padding: 0 20px 0 0;
  }
  .el-tabs__content {
    height: 100%;
    padding: 0 20px 0 10px;
    overflow: auto;
  }

  .tool-operations {
    margin: 20px 0;
  }
  .output {
    padding: 10px;
    border: 1px solid $border-color-dark;
    line-height: 1.5;
    word-wrap: break-word;
    word-break: break-all;
  }
}
</style>

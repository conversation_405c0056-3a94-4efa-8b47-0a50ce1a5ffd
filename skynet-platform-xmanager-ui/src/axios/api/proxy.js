import locale from '@/i18n/index.js'
import axios from 'axios'
import { ELMessage } from '@/common/util'
import MD5 from 'md5.js'
import { encryptUrl } from '@/utils/auth'
const timeout = process.env.VUE_APP_REQ_TIMEOUT ? parseInt(process.env.VUE_APP_REQ_TIMEOUT) : 30000

/**
 * 由于代理请求返回的响应结构与api接口不同，因此这里不用import的request。
 */
const request = axios.create({
  baseURL: process.env.VUE_APP_BASE_URL,
  timeout // request timeout
})
function proxyFetch(url) {
  let md5sum = new MD5().update(url).digest('hex')
  let eUrl = encryptUrl(url)
  let proxyUrl = `skynet/proxy/http?u=${eUrl}`
  /* let title = encodeURIComponent(url) */
  window.sessionStorage.setItem(md5sum, proxyUrl)
  window.open(`/#/fetcher/${md5sum}`)
  /* window.open(`/#/fetcher/${md5sum}?url=${proxyUrl}&title=${title}`)*/
}
function proxyGet(url) {
  let isTextType = function(contentType) {
    let ret = false
    if (!contentType) {
      return ret
    }
    const keyWords = ['text', 'json', 'html']
    for (let k of keyWords) {
      if (contentType.indexOf(k) >= 0) {
        ret = true
        break
      }
    }
    return ret
  }
  let getContentType = function(headers) {
    for (let k in headers) {
      if (k.toLocaleLowerCase() === 'content-type') {
        return headers[k]
      }
    }
    return null
  }
  return new Promise((resolv, reject) => {
    let eUrl = encryptUrl(url)
    let proxyUrl = `skynet/proxy/http?u=${eUrl}`
    request
      .get(proxyUrl, {
        responseType: 'blob'
      })
      .then(resp => {
        let contentType = getContentType(resp.headers)
        if (isTextType(contentType)) {
          let reader = new FileReader()
          reader.readAsText(resp.data, 'utf-8')
          reader.onload = function() {
            resolv(reader.result)
          }
        } else {
          window.open(proxyUrl)
          resolv(null)
        }
      })
      .catch(error => {
        ELMessage(locale.t('45'), 'error')
        console.error(error)
      })
  })
}
function proxyPost(url, data) {
  return proxyRequest(url, 'POST', data)
}
function proxyRequest(url, method, data) {
  return new Promise((resolv, reject) => {
    let eUrl = encryptUrl(url)
    let proxyUrl = `skynet/proxy/http?u=${eUrl}`
    request[method.toLowerCase()](proxyUrl, data)
      .then(resp => {
        resolv(resp)
      })
      .catch(error => {
        ELMessage(locale.t('45'), 'error')
        console.error(error)
        // reject(error)
      })
  })
}
export default {
  proxyGet,
  proxyPost,
  proxyFetch,
  proxyRequest
}

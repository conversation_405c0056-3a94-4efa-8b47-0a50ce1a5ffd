import locale from '@/i18n/index.js'
import request from '../request'
function getClusterInfo() {
  return request.get('skynet/api/v3/cluster/info')
}
function getClusterProps() {
  return request.get('skynet/api/v3/cluster/properties')
}
function getClusterLoggingLevels() {
  return request.get('skynet/api/v3/cluster/logging-levels')
}
function updateClusterProps(data) {
  return request.put('skynet/api/v3/cluster/properties', data, {
    __view_pop_success: locale.t('17'),
    __view_pop_error: locale.t('18')
  })
}
function updateClusterLoggingLevels(data) {
  return request.put('skynet/api/v3/cluster/logging-levels', data, {
    __view_pop_success: locale.t('19'),
    __view_pop_error: locale.t('20')
  })
}
export default {
  getClusterInfo,
  getClusterProps,
  getClusterLoggingLevels,
  updateClusterProps,
  updateClusterLoggingLevels
}

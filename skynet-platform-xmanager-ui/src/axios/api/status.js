import locale from '@/i18n/index.js'
import request from '../request'
function startAction(dtoList) {
  return __update_action_status(dtoList)
}
function stopAction(dtoList) {
  return __update_action_status(dtoList)
}
function rebootAction(dtoList) {
  if (!dtoList || dtoList.length === 0) {
    return Promise.resolve([])
  }
  return request.post(`skynet/api/v3/actions/status/reboot`, dtoList)
}
function __update_action_status(dtoList) {
  return request.put('skynet/api/v3/actions/status', dtoList)
}
export default {
  startAction,
  stopAction,
  rebootAction
}

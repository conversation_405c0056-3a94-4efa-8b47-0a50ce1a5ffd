import locale from '@/i18n/index.js'
// import Cookies from 'js-cookie'
// let time = new Date().getTime().toString()
// let accessToken = Cookies.get('accessToken')
// let token = accessToken ? accessToken : "";
export default {
  timeout: 10000,
  withCredentials: true,
  responseType: 'json',
  // Authorization: token ? `Bearer ${token}` : "",
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
    Cookie: 'perm=23D08E648505F559726C8811B9B59C95'
  }
}

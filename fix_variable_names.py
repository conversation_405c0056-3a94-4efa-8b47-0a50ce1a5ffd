#!/usr/bin/env python3
"""
修复变量名问题
"""

import os
import re
import glob

def fix_variable_names_in_file(file_path):
    """修复单个文件中的变量名问题"""
    print(f"正在修复变量名: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 修复变量名问题
    # 在 listNamespaced* 方法中，应该使用 k8sQuery.getNamespace() 而不是 namespace
    patterns_to_fix = [
        (r'api\.listNamespacedJob\(namespace\)', 'api.listNamespacedJob(k8sQuery.getNamespace())'),
        (r'api\.listNamespacedPod\(namespace\)', 'api.listNamespacedPod(k8sQuery.getNamespace())'),
        (r'api\.listNamespacedDeployment\(namespace\)', 'api.listNamespacedDeployment(k8sQuery.getNamespace())'),
        (r'api\.listNamespacedDaemonSet\(namespace\)', 'api.listNamespacedDaemonSet(k8sQuery.getNamespace())'),
        (r'api\.listNamespacedStatefulSet\(namespace\)', 'api.listNamespacedStatefulSet(k8sQuery.getNamespace())'),
        (r'api\.listNamespacedReplicaSet\(namespace\)', 'api.listNamespacedReplicaSet(k8sQuery.getNamespace())'),
        (r'api\.listNamespacedService\(namespace\)', 'api.listNamespacedService(k8sQuery.getNamespace())'),
        (r'api\.listNamespacedConfigMap\(namespace\)', 'api.listNamespacedConfigMap(k8sQuery.getNamespace())'),
        (r'api\.listNamespacedSecret\(namespace\)', 'api.listNamespacedSecret(k8sQuery.getNamespace())'),
        (r'api\.listNamespacedEndpoints\(namespace\)', 'api.listNamespacedEndpoints(k8sQuery.getNamespace())'),
        (r'api\.listNamespacedEvent\(namespace\)', 'api.listNamespacedEvent(k8sQuery.getNamespace())'),
        (r'api\.listNamespacedIngress\(namespace\)', 'api.listNamespacedIngress(k8sQuery.getNamespace())'),
        (r'api\.listNamespacedCronJob\(namespace\)', 'api.listNamespacedCronJob(k8sQuery.getNamespace())'),
        (r'api\.listNamespacedHorizontalPodAutoscaler\(namespace\)', 'api.listNamespacedHorizontalPodAutoscaler(k8sQuery.getNamespace())'),
    ]
    
    for pattern, replacement in patterns_to_fix:
        content = re.sub(pattern, replacement, content)
    
    # 修复 patch 方法的参数问题
    # 移除多余的 null 参数
    patch_patterns = [
        (r'api\.patchNamespacedDeployment\(([^,]+),\s*([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null\)', r'api.patchNamespacedDeployment(\1, \2, \3)'),
        (r'api\.patchNamespacedDaemonSet\(([^,]+),\s*([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null\)', r'api.patchNamespacedDaemonSet(\1, \2, \3)'),
        (r'api\.patchNamespacedStatefulSet\(([^,]+),\s*([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null\)', r'api.patchNamespacedStatefulSet(\1, \2, \3)'),
        (r'api\.patchNamespacedCronJob\(([^,]+),\s*([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null\)', r'api.patchNamespacedCronJob(\1, \2, \3)'),
    ]
    
    for pattern, replacement in patch_patterns:
        content = re.sub(pattern, replacement, content)
    
    # 保存修改后的文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✓ 已修复变量名 {file_path}")
        return True
    else:
        print(f"  - 无需修改 {file_path}")
        return False

def main():
    """主函数"""
    # 查找所有需要修复的 Java 文件
    java_files = glob.glob("skynet-platform-xmanager/src/main/java/skynet/platform/manager/k8s/service/impl/*.java")
    java_files.extend(glob.glob("skynet-platform-xmanager/src/main/java/skynet/platform/manager/admin/v3/service/*.java"))
    
    fixed_count = 0
    total_count = len(java_files)
    
    print(f"找到 {total_count} 个 Java 文件需要检查变量名...")
    
    for java_file in java_files:
        if fix_variable_names_in_file(java_file):
            fixed_count += 1
    
    print(f"\n变量名修复完成！共修复了 {fixed_count}/{total_count} 个文件。")

if __name__ == "__main__":
    main()

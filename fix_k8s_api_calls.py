#!/usr/bin/env python3
"""
脚本用于修复 Kubernetes client-java 24.0.0 版本的 API 调用
移除多余的 null 参数，使用简化的方法签名
"""

import os
import re
import glob

def fix_api_calls_in_file(file_path):
    """修复单个文件中的 API 调用"""
    print(f"正在修复文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 修复各种 API 调用模式
    
    # 1. listNode() - 移除所有参数
    content = re.sub(
        r'api\.listNode\([^)]*\)',
        'api.listNode()',
        content
    )
    
    # 2. readNode() - 只保留 name 参数
    content = re.sub(
        r'api\.readNode\(([^,]+),\s*null\)',
        r'api.readNode(\1)',
        content
    )
    
    # 3. replaceNode() - 只保留 name 和 body 参数
    content = re.sub(
        r'api\.replaceNode\(([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null\)',
        r'api.replaceNode(\1, \2)',
        content
    )
    
    # 4. deleteNode() - 只保留 name 参数
    content = re.sub(
        r'api\.deleteNode\(([^,]+),\s*null,\s*null,\s*null,\s*null,\s*null,\s*null\)',
        r'api.deleteNode(\1)',
        content
    )
    
    # 5. patchNode() - 只保留 name 和 patch 参数
    content = re.sub(
        r'api\.patchNode\(([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null\)',
        r'api.patchNode(\1, \2)',
        content
    )
    
    # 6. listNamespace() - 移除所有参数
    content = re.sub(
        r'api\.listNamespace\([^)]*\)',
        'api.listNamespace()',
        content
    )
    
    # 7. readNamespace() - 只保留 name 参数
    content = re.sub(
        r'api\.readNamespace\(([^,]+),\s*null\)',
        r'api.readNamespace(\1)',
        content
    )
    
    # 8. deleteNamespace() - 只保留 name 参数
    content = re.sub(
        r'api\.deleteNamespace\(([^,]+),\s*null,\s*null,\s*null,\s*null,\s*null,\s*null\)',
        r'api.deleteNamespace(\1)',
        content
    )
    
    # 9. listNamespacedPod() - 只保留 namespace 参数
    content = re.sub(
        r'api\.listNamespacedPod\(([^,]+),\s*null,\s*null,\s*null,\s*[^,]+,\s*[^,]+,\s*null,\s*null,\s*null,\s*null,\s*null\)',
        r'api.listNamespacedPod(\1)',
        content
    )
    
    # 10. listPodForAllNamespaces() - 移除所有参数
    content = re.sub(
        r'api\.listPodForAllNamespaces\([^)]*\)',
        'api.listPodForAllNamespaces()',
        content
    )
    
    # 11. readNamespacedPod() - 只保留 name 和 namespace 参数
    content = re.sub(
        r'api\.readNamespacedPod\(([^,]+),\s*([^,]+),\s*null\)',
        r'api.readNamespacedPod(\1, \2)',
        content
    )
    
    # 12. deleteNamespacedPod() - 只保留 name 和 namespace 参数
    content = re.sub(
        r'api\.deleteNamespacedPod\(([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null,\s*null,\s*null\)',
        r'api.deleteNamespacedPod(\1, \2)',
        content
    )
    
    # 13. listNamespacedDeployment() - 只保留 namespace 参数
    content = re.sub(
        r'api\.listNamespacedDeployment\(([^,]+),\s*null,\s*null,\s*null,\s*[^,]+,\s*[^,]+,\s*null,\s*null,\s*null,\s*null,\s*null\)',
        r'api.listNamespacedDeployment(\1)',
        content
    )
    
    # 14. listDeploymentForAllNamespaces() - 移除所有参数
    content = re.sub(
        r'api\.listDeploymentForAllNamespaces\([^)]*\)',
        'api.listDeploymentForAllNamespaces()',
        content
    )
    
    # 15. readNamespacedDeployment() - 只保留 name 和 namespace 参数
    content = re.sub(
        r'api\.readNamespacedDeployment\(([^,]+),\s*([^,]+),\s*null\)',
        r'api.readNamespacedDeployment(\1, \2)',
        content
    )
    
    # 16. deleteNamespacedDeployment() - 只保留 name 和 namespace 参数
    content = re.sub(
        r'api\.deleteNamespacedDeployment\(([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null,\s*null,\s*null\)',
        r'api.deleteNamespacedDeployment(\1, \2)',
        content
    )
    
    # 17. patchNamespacedDeployment() - 只保留 name, namespace 和 patch 参数
    content = re.sub(
        r'api\.patchNamespacedDeployment\(([^,]+),\s*([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null\)',
        r'api.patchNamespacedDeployment(\1, \2, \3)',
        content
    )
    
    # 18. readNamespacedReplicaSet() - 只保留 name 和 namespace 参数
    content = re.sub(
        r'api\.readNamespacedReplicaSet\(([^,]+),\s*([^,]+),\s*null\)',
        r'api.readNamespacedReplicaSet(\1, \2)',
        content
    )

    # 19. 修复更多的 list 方法
    patterns_to_fix = [
        # DaemonSet
        (r'api\.listNamespacedDaemonSet\([^)]*\)', 'api.listNamespacedDaemonSet(namespace)'),
        (r'api\.listDaemonSetForAllNamespaces\([^)]*\)', 'api.listDaemonSetForAllNamespaces()'),
        (r'api\.readNamespacedDaemonSet\(([^,]+),\s*([^,]+),\s*null\)', r'api.readNamespacedDaemonSet(\1, \2)'),
        (r'api\.deleteNamespacedDaemonSet\(([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null,\s*null,\s*null\)', r'api.deleteNamespacedDaemonSet(\1, \2)'),
        (r'api\.patchNamespacedDaemonSet\(([^,]+),\s*([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null\)', r'api.patchNamespacedDaemonSet(\1, \2, \3)'),

        # StatefulSet
        (r'api\.listNamespacedStatefulSet\([^)]*\)', 'api.listNamespacedStatefulSet(namespace)'),
        (r'api\.listStatefulSetForAllNamespaces\([^)]*\)', 'api.listStatefulSetForAllNamespaces()'),
        (r'api\.readNamespacedStatefulSet\(([^,]+),\s*([^,]+),\s*null\)', r'api.readNamespacedStatefulSet(\1, \2)'),
        (r'api\.deleteNamespacedStatefulSet\(([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null,\s*null,\s*null\)', r'api.deleteNamespacedStatefulSet(\1, \2)'),
        (r'api\.patchNamespacedStatefulSet\(([^,]+),\s*([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null\)', r'api.patchNamespacedStatefulSet(\1, \2, \3)'),

        # Job
        (r'api\.listNamespacedJob\([^)]*\)', 'api.listNamespacedJob(namespace)'),
        (r'api\.listJobForAllNamespaces\([^)]*\)', 'api.listJobForAllNamespaces()'),
        (r'api\.readNamespacedJob\(([^,]+),\s*([^,]+),\s*null\)', r'api.readNamespacedJob(\1, \2)'),
        (r'api\.deleteNamespacedJob\(([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null,\s*null,\s*null\)', r'api.deleteNamespacedJob(\1, \2)'),
        (r'api\.createNamespacedJob\(([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null\)', r'api.createNamespacedJob(\1, \2)'),

        # CronJob
        (r'api\.listNamespacedCronJob\([^)]*\)', 'api.listNamespacedCronJob(namespace)'),
        (r'api\.listCronJobForAllNamespaces\([^)]*\)', 'api.listCronJobForAllNamespaces()'),
        (r'api\.readNamespacedCronJob\(([^,]+),\s*([^,]+),\s*null\)', r'api.readNamespacedCronJob(\1, \2)'),
        (r'api\.deleteNamespacedCronJob\(([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null,\s*null,\s*null\)', r'api.deleteNamespacedCronJob(\1, \2)'),
        (r'api\.patchNamespacedCronJob\(([^,]+),\s*([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null\)', r'api.patchNamespacedCronJob(\1, \2, \3)'),

        # ReplicaSet
        (r'api\.listNamespacedReplicaSet\([^)]*\)', 'api.listNamespacedReplicaSet(namespace)'),
        (r'api\.listReplicaSetForAllNamespaces\([^)]*\)', 'api.listReplicaSetForAllNamespaces()'),
        (r'api\.deleteNamespacedReplicaSet\(([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null,\s*null,\s*null\)', r'api.deleteNamespacedReplicaSet(\1, \2)'),

        # Service
        (r'api\.listNamespacedService\([^)]*\)', 'api.listNamespacedService(namespace)'),
        (r'api\.listServiceForAllNamespaces\([^)]*\)', 'api.listServiceForAllNamespaces()'),
        (r'api\.readNamespacedService\(([^,]+),\s*([^,]+),\s*null\)', r'api.readNamespacedService(\1, \2)'),
        (r'api\.deleteNamespacedService\(([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null,\s*null,\s*null\)', r'api.deleteNamespacedService(\1, \2)'),

        # ConfigMap
        (r'api\.listNamespacedConfigMap\([^)]*\)', 'api.listNamespacedConfigMap(namespace)'),
        (r'api\.listConfigMapForAllNamespaces\([^)]*\)', 'api.listConfigMapForAllNamespaces()'),
        (r'api\.readNamespacedConfigMap\(([^,]+),\s*([^,]+),\s*null\)', r'api.readNamespacedConfigMap(\1, \2)'),
        (r'api\.deleteNamespacedConfigMap\(([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null,\s*null,\s*null\)', r'api.deleteNamespacedConfigMap(\1, \2)'),

        # Secret
        (r'api\.listNamespacedSecret\([^)]*\)', 'api.listNamespacedSecret(namespace)'),
        (r'api\.listSecretForAllNamespaces\([^)]*\)', 'api.listSecretForAllNamespaces()'),
        (r'api\.readNamespacedSecret\(([^,]+),\s*([^,]+),\s*null\)', r'api.readNamespacedSecret(\1, \2)'),
        (r'api\.deleteNamespacedSecret\(([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null,\s*null,\s*null\)', r'api.deleteNamespacedSecret(\1, \2)'),

        # Endpoints
        (r'api\.listNamespacedEndpoints\([^)]*\)', 'api.listNamespacedEndpoints(namespace)'),
        (r'api\.listEndpointsForAllNamespaces\([^)]*\)', 'api.listEndpointsForAllNamespaces()'),
        (r'api\.readNamespacedEndpoints\(([^,]+),\s*([^,]+),\s*null\)', r'api.readNamespacedEndpoints(\1, \2)'),
        (r'api\.deleteNamespacedEndpoints\(([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null,\s*null,\s*null\)', r'api.deleteNamespacedEndpoints(\1, \2)'),

        # Event
        (r'api\.listNamespacedEvent\([^)]*\)', 'api.listNamespacedEvent(namespace)'),
        (r'api\.listEventForAllNamespaces\([^)]*\)', 'api.listEventForAllNamespaces()'),
        (r'api\.readNamespacedEvent\(([^,]+),\s*([^,]+),\s*null\)', r'api.readNamespacedEvent(\1, \2)'),
        (r'api\.deleteNamespacedEvent\(([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null,\s*null,\s*null\)', r'api.deleteNamespacedEvent(\1, \2)'),

        # Ingress
        (r'api\.listNamespacedIngress\([^)]*\)', 'api.listNamespacedIngress(namespace)'),
        (r'api\.listIngressForAllNamespaces\([^)]*\)', 'api.listIngressForAllNamespaces()'),
        (r'api\.readNamespacedIngress\(([^,]+),\s*([^,]+),\s*null\)', r'api.readNamespacedIngress(\1, \2)'),
        (r'api\.deleteNamespacedIngress\(([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null,\s*null,\s*null\)', r'api.deleteNamespacedIngress(\1, \2)'),

        # HorizontalPodAutoscaler
        (r'api\.listNamespacedHorizontalPodAutoscaler\([^)]*\)', 'api.listNamespacedHorizontalPodAutoscaler(namespace)'),
        (r'api\.listHorizontalPodAutoscalerForAllNamespaces\([^)]*\)', 'api.listHorizontalPodAutoscalerForAllNamespaces()'),
        (r'api\.readNamespacedHorizontalPodAutoscaler\(([^,]+),\s*([^,]+),\s*null\)', r'api.readNamespacedHorizontalPodAutoscaler(\1, \2)'),
        (r'api\.deleteNamespacedHorizontalPodAutoscaler\(([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*null,\s*null,\s*null\)', r'api.deleteNamespacedHorizontalPodAutoscaler(\1, \2)'),

        # CustomResourceDefinition
        (r'api\.listCustomResourceDefinition\([^)]*\)', 'api.listCustomResourceDefinition()'),
        (r'api\.readCustomResourceDefinition\(([^,]+),\s*null\)', r'api.readCustomResourceDefinition(\1)'),
        (r'api\.deleteCustomResourceDefinition\(([^,]+),\s*null,\s*null,\s*null,\s*null,\s*null,\s*null\)', r'api.deleteCustomResourceDefinition(\1)'),

        # CustomObject
        (r'api\.listNamespacedCustomObject\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*null,\s*null,\s*null,\s*[^,]+,\s*[^,]+,\s*null,\s*null,\s*null,\s*null,\s*null\)', r'api.listNamespacedCustomObject(\1, \2, \3, \4)'),
        (r'api\.listClusterCustomObject\(([^,]+),\s*([^,]+),\s*([^,]+),\s*null,\s*null,\s*[^,]+,\s*[^,]+,\s*null,\s*null,\s*null,\s*null,\s*null,\s*null\)', r'api.listClusterCustomObject(\1, \2, \3)'),
    ]

    for pattern, replacement in patterns_to_fix:
        content = re.sub(pattern, replacement, content)
    
    # 保存修改后的文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✓ 已修复 {file_path}")
        return True
    else:
        print(f"  - 无需修改 {file_path}")
        return False

def main():
    """主函数"""
    # 查找所有需要修复的 Java 文件
    java_files = glob.glob("skynet-platform-xmanager/src/main/java/skynet/platform/manager/k8s/service/impl/*.java")
    
    # 还需要修复其他目录中的文件
    java_files.extend(glob.glob("skynet-platform-xmanager/src/main/java/skynet/platform/manager/admin/v3/service/*.java"))
    
    fixed_count = 0
    total_count = len(java_files)
    
    print(f"找到 {total_count} 个 Java 文件需要检查...")
    
    for java_file in java_files:
        if fix_api_calls_in_file(java_file):
            fixed_count += 1
    
    print(f"\n修复完成！共修复了 {fixed_count}/{total_count} 个文件。")

if __name__ == "__main__":
    main()

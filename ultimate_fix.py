#!/usr/bin/env python3
"""
终极修复脚本 - 修复所有 kubernetes-client-java 24.0.0 版本的 API 调用
"""

import os
import re
import glob

def ultimate_fix_in_file(file_path):
    """终极修复单个文件"""
    print(f"正在终极修复: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 1. 为所有 API 调用添加 .execute()
    # 匹配模式：变量 = api.方法名(参数); 但不包括已经有 .execute() 的
    patterns = [
        # 基本的 API 调用模式
        r'(\w+\s*=\s*api\.[a-zA-Z][a-zA-Z0-9]*\([^)]*\))(?!\.execute\(\))(\s*;)',
        # Kubectl 调用模式
        r'(\w+\s*=\s*Kubectl\.[a-zA-Z][a-zA-Z0-9]*\([^)]*\))(?!\.execute\(\))(\s*;)',
    ]
    
    for pattern in patterns:
        content = re.sub(pattern, r'\1.execute()\2', content)
    
    # 2. 修复一些特殊情况
    # 移除在字符串或其他非 API 对象上的 .execute() 调用
    content = re.sub(r'k8sQuery\.getNamespace\(\)\.execute\(\)', 'k8sQuery.getNamespace()', content)
    content = re.sub(r'toJSONString\([^)]+\)\.execute\(\)', lambda m: m.group(0).replace('.execute()', ''), content)
    content = re.sub(r'patch\.toString\(\)\.execute\(\)', 'patch.toString()', content)
    
    # 3. 修复 Kubectl 的特殊调用
    # Kubectl 的方法调用方式在 24.0.0 中有变化
    content = re.sub(
        r'(\w+\s*=\s*)Kubectl\.cordon\(([^)]+)\)\.apiClient\([^)]+\)\.execute\(\)',
        r'\1Kubectl.cordon(\2).execute()',
        content
    )
    content = re.sub(
        r'(\w+\s*=\s*)Kubectl\.uncordon\(([^)]+)\)\.apiClient\([^)]+\)\.execute\(\)',
        r'\1Kubectl.uncordon(\2).execute()',
        content
    )
    content = re.sub(
        r'(\w+\s*=\s*)Kubectl\.drain\(([^)]+)\)\.apiClient\([^)]+\)\.ignoreDaemonSets\(\)\.execute\(\)',
        r'\1Kubectl.drain(\2).execute()',
        content
    )
    
    # 4. 修复一些错误的变量赋值
    # 移除在已经是对象的变量上的 .execute()
    content = re.sub(r'(V1\w+List\s+\w+\s*=\s*[^;]+)\.execute\(\);', r'\1;', content)
    content = re.sub(r'(V1\w+\s+\w+\s*=\s*[^;]+)\.execute\(\);', r'\1;', content)
    content = re.sub(r'(V1Status\s+\w+\s*=\s*[^;]+)\.execute\(\);', r'\1;', content)
    content = re.sub(r'(\w+List\s+\w+\s*=\s*[^;]+)\.execute\(\);', r'\1;', content)
    
    # 保存修改后的文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✓ 已终极修复 {file_path}")
        return True
    else:
        print(f"  - 无需修改 {file_path}")
        return False

def main():
    """主函数"""
    # 查找所有需要修复的 Java 文件
    java_files = glob.glob("skynet-platform-xmanager/src/main/java/skynet/platform/manager/k8s/service/impl/*.java")
    
    fixed_count = 0
    total_count = len(java_files)
    
    print(f"找到 {total_count} 个 Java 文件需要终极修复...")
    
    for java_file in java_files:
        if ultimate_fix_in_file(java_file):
            fixed_count += 1
    
    print(f"\n终极修复完成！共修复了 {fixed_count}/{total_count} 个文件。")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
修复 kubernetes-client-java 24.0.0 版本的 API 调用
在所有 API 方法调用后添加 .execute()
"""

import os
import re
import glob

def fix_execute_calls_in_file(file_path):
    """修复单个文件中的 API 调用，添加 .execute()"""
    print(f"正在修复 API 调用: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 定义需要添加 .execute() 的 API 方法模式
    api_patterns = [
        # CoreV1Api methods
        r'(api\.listNode\(\))',
        r'(api\.readNode\([^)]+\))',
        r'(api\.replaceNode\([^)]+\))',
        r'(api\.deleteNode\([^)]+\))',
        r'(api\.patchNode\([^)]+\))',
        
        r'(api\.listNamespace\(\))',
        r'(api\.readNamespace\([^)]+\))',
        r'(api\.deleteNamespace\([^)]+\))',
        
        r'(api\.listNamespacedPod\([^)]+\))',
        r'(api\.listPodForAllNamespaces\(\))',
        r'(api\.readNamespacedPod\([^)]+\))',
        r'(api\.deleteNamespacedPod\([^)]+\))',
        
        r'(api\.listNamespacedService\([^)]+\))',
        r'(api\.listServiceForAllNamespaces\(\))',
        r'(api\.readNamespacedService\([^)]+\))',
        r'(api\.deleteNamespacedService\([^)]+\))',
        
        r'(api\.listNamespacedConfigMap\([^)]+\))',
        r'(api\.listConfigMapForAllNamespaces\(\))',
        r'(api\.readNamespacedConfigMap\([^)]+\))',
        r'(api\.deleteNamespacedConfigMap\([^)]+\))',
        
        r'(api\.listNamespacedSecret\([^)]+\))',
        r'(api\.listSecretForAllNamespaces\(\))',
        r'(api\.readNamespacedSecret\([^)]+\))',
        r'(api\.deleteNamespacedSecret\([^)]+\))',
        
        r'(api\.listNamespacedEndpoints\([^)]+\))',
        r'(api\.listEndpointsForAllNamespaces\(\))',
        r'(api\.readNamespacedEndpoints\([^)]+\))',
        r'(api\.deleteNamespacedEndpoints\([^)]+\))',
        
        r'(api\.listNamespacedEvent\([^)]+\))',
        r'(api\.listEventForAllNamespaces\(\))',
        r'(api\.readNamespacedEvent\([^)]+\))',
        r'(api\.deleteNamespacedEvent\([^)]+\))',
        
        # AppsV1Api methods
        r'(api\.listNamespacedDeployment\([^)]+\))',
        r'(api\.listDeploymentForAllNamespaces\(\))',
        r'(api\.readNamespacedDeployment\([^)]+\))',
        r'(api\.deleteNamespacedDeployment\([^)]+\))',
        r'(api\.patchNamespacedDeployment\([^)]+\))',
        
        r'(api\.listNamespacedDaemonSet\([^)]+\))',
        r'(api\.listDaemonSetForAllNamespaces\(\))',
        r'(api\.readNamespacedDaemonSet\([^)]+\))',
        r'(api\.deleteNamespacedDaemonSet\([^)]+\))',
        r'(api\.patchNamespacedDaemonSet\([^)]+\))',
        
        r'(api\.listNamespacedStatefulSet\([^)]+\))',
        r'(api\.listStatefulSetForAllNamespaces\(\))',
        r'(api\.readNamespacedStatefulSet\([^)]+\))',
        r'(api\.deleteNamespacedStatefulSet\([^)]+\))',
        r'(api\.patchNamespacedStatefulSet\([^)]+\))',
        
        r'(api\.listNamespacedReplicaSet\([^)]+\))',
        r'(api\.listReplicaSetForAllNamespaces\(\))',
        r'(api\.readNamespacedReplicaSet\([^)]+\))',
        r'(api\.deleteNamespacedReplicaSet\([^)]+\))',
        
        # BatchV1Api methods
        r'(api\.listNamespacedJob\([^)]+\))',
        r'(api\.listJobForAllNamespaces\(\))',
        r'(api\.readNamespacedJob\([^)]+\))',
        r'(api\.deleteNamespacedJob\([^)]+\))',
        r'(api\.createNamespacedJob\([^)]+\))',
        
        r'(api\.listNamespacedCronJob\([^)]+\))',
        r'(api\.listCronJobForAllNamespaces\(\))',
        r'(api\.readNamespacedCronJob\([^)]+\))',
        r'(api\.deleteNamespacedCronJob\([^)]+\))',
        r'(api\.patchNamespacedCronJob\([^)]+\))',
        
        # NetworkingV1Api methods
        r'(api\.listNamespacedIngress\([^)]+\))',
        r'(api\.listIngressForAllNamespaces\(\))',
        r'(api\.readNamespacedIngress\([^)]+\))',
        r'(api\.deleteNamespacedIngress\([^)]+\))',
        
        # AutoscalingV2Api methods
        r'(api\.listNamespacedHorizontalPodAutoscaler\([^)]+\))',
        r'(api\.listHorizontalPodAutoscalerForAllNamespaces\(\))',
        r'(api\.readNamespacedHorizontalPodAutoscaler\([^)]+\))',
        r'(api\.deleteNamespacedHorizontalPodAutoscaler\([^)]+\))',
        
        # ApiextensionsV1Api methods
        r'(api\.listCustomResourceDefinition\(\))',
        r'(api\.readCustomResourceDefinition\([^)]+\))',
        r'(api\.deleteCustomResourceDefinition\([^)]+\))',
    ]
    
    # 为每个模式添加 .execute()
    for pattern in api_patterns:
        content = re.sub(pattern, r'\1.execute()', content)
    
    # 修复一些特殊情况
    # 修复变量名错误
    content = re.sub(r'(\w+\.getNamespace\(\))', r'k8sQuery.getNamespace()', content)
    
    # 保存修改后的文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✓ 已修复 API 调用 {file_path}")
        return True
    else:
        print(f"  - 无需修改 {file_path}")
        return False

def main():
    """主函数"""
    # 查找所有需要修复的 Java 文件
    java_files = glob.glob("skynet-platform-xmanager/src/main/java/skynet/platform/manager/k8s/service/impl/*.java")
    java_files.extend(glob.glob("skynet-platform-xmanager/src/main/java/skynet/platform/manager/admin/v3/service/*.java"))
    
    fixed_count = 0
    total_count = len(java_files)
    
    print(f"找到 {total_count} 个 Java 文件需要检查 API 调用...")
    
    for java_file in java_files:
        if fix_execute_calls_in_file(java_file):
            fixed_count += 1
    
    print(f"\nAPI 调用修复完成！共修复了 {fixed_count}/{total_count} 个文件。")

if __name__ == "__main__":
    main()

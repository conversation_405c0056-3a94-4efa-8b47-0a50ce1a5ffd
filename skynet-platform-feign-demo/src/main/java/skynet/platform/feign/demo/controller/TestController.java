package skynet.platform.feign.demo.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.AccessToken;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.AuthLogin;


@Slf4j
@RestController
@ExposeSwagger2
public class TestController {
    private final AuthLogin authLogin;

    public TestController(AuthLogin authLogin) {
        this.authLogin = authLogin;
    }

    @GetMapping("/test")
    public Object connection() throws Exception {
        SkynetApiResponse<AccessToken> tokenRestResponse = authLogin.token();
        long begin = System.currentTimeMillis();
        for (int i = 0; i < 10; i++) {
            log.debug("{}", tokenRestResponse);
        }
        begin = System.currentTimeMillis() - begin;
        return begin;
    }
}

package skynet.xmanager;

import org.apache.commons.io.FileUtils;
import org.springframework.mock.env.MockEnvironment;
import skynet.boot.SkynetProperties;
import skynet.boot.zookeeper.SkynetZkProperties;
import skynet.boot.zookeeper.ZkConfigService;
import skynet.boot.zookeeper.impl.ZkConfigServiceImpl;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.config.impl.AntConfigServiceImpl;
import skynet.platform.manager.admin.service.PluginZkTextService;

import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/14 21:36
 */
public class PluginZkTextServiceTest {


    public static void main(String[] args) throws Exception {

        SkynetZkProperties skynetZkProperties = new SkynetZkProperties();
        skynetZkProperties.setServerList("127.0.0.1:2181");
        MockEnvironment mockEnvironment = new MockEnvironment();
        SkynetProperties skynetProperties = new SkynetProperties(mockEnvironment);
        skynetProperties.setActionPoint("a@ant");

        try (ZkConfigService zkConfigService = new ZkConfigServiceImpl(skynetZkProperties)) {
            try (IAntConfigService antConfigService = new AntConfigServiceImpl(zkConfigService, skynetProperties)) {
                PluginZkTextService pluginZkTextService = new PluginZkTextService(antConfigService);
                String plugin = "ant";
                String zkConfig = pluginZkTextService.exportZkConfig(plugin);

                System.out.println(zkConfig);

                File file = new File("e:/abc.txt");
                FileUtils.write(file, zkConfig, "utf-8");

                List<String> zkTxt = FileUtils.readLines(file, "utf-8");

                pluginZkTextService.importZkConfig(zkTxt);
            }
        }
    }
}

package skynet.platform.manager.admin.v3.service;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.models.V1Node;
import io.kubernetes.client.openapi.models.V1NodeAddress;
import io.kubernetes.client.openapi.models.V1NodeList;
import io.kubernetes.client.util.ClientBuilder;
import io.kubernetes.client.util.KubeConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import skynet.boot.AppContext;
import skynet.boot.security.SkynetEncryption;
import skynet.boot.security.client.BaseAuthRestTemplateBuilder;
import skynet.platform.common.AppVersionBuilder;
import skynet.platform.common.auth.AuthClientProperties;
import skynet.platform.common.domain.AntServerParam;
import skynet.platform.common.domain.AntServerProperty;
import skynet.platform.common.domain.ServerLoginParam;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.utils.DockerLoginCheckerUtils;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;
import skynet.platform.feign.model.AgentDto;
import skynet.platform.feign.model.AgentType;
import skynet.platform.feign.model.AgentVersionDto;
import skynet.platform.manager.admin.config.ManagerProperties;
import skynet.platform.common.auth.ManagerEncryptor;
import skynet.platform.manager.admin.service.deploy.KubernetesDeployClient;
import skynet.platform.manager.exception.AgentNotRegisterException;
import skynet.platform.manager.k8s.config.K8sConfigProperties;
import skynet.platform.manager.k8s.service.impl.K8sKubectlService;

import java.io.StringReader;
import java.math.BigDecimal;
import java.util.Base64;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Kubernetes 节点管理
 */
@Slf4j
@Service
public class V3KubernetesAgentService extends V3BaseAgentService {

    private final AppContext appContext;
    private final BaseAuthRestTemplateBuilder baseAuthRestTemplateBuilder;
    private final K8sKubectlService k8sKubectlService;
    private final AuthClientProperties authClientProperties;
    private final K8sConfigProperties k8sConfigProperties;
    private final AppVersionBuilder appVersionBuilder;
    private final Cache<String, Map<String, Object>> agentServerInfoCache;

    public V3KubernetesAgentService(AppContext appContext, BaseAuthRestTemplateBuilder baseAuthRestTemplateBuilder, K8sKubectlService k8sKubectlService,
                                    ManagerEncryptor managerEncryptor, SkynetEncryption skynetEncryption,
                                    IAntConfigService antConfigService,
                                    ManagerProperties managerProperties,
                                    AuthClientProperties authClientProperties, K8sConfigProperties k8sConfigProperties,
                                    AppVersionBuilder appVersionBuilder) {
        super(managerEncryptor, skynetEncryption, antConfigService, managerProperties);
        this.appContext = appContext;
        this.baseAuthRestTemplateBuilder = baseAuthRestTemplateBuilder;
        this.k8sKubectlService = k8sKubectlService;
        this.authClientProperties = authClientProperties;
        this.k8sConfigProperties = k8sConfigProperties;
        this.appVersionBuilder = appVersionBuilder;
        this.agentServerInfoCache = CacheBuilder.newBuilder()
                .maximumSize(100)
                .expireAfterAccess(5, TimeUnit.MINUTES)
                .build();
    }

    /**
     * 校验 AgentDto 参数格式
     */
    @Override
    protected void checkAgentDto(AgentDto agentDto) {
        if (StringUtils.isBlank(agentDto.getKubeConfig()) ||
                StringUtils.isBlank(agentDto.getRegistryUrl())) {
            throw new ApiRequestException(ApiRequestErrorCode.PARAM_ILLEGAL);
        }
    }

    /**
     * 测试连接 Kubernetes 是否成功
     * 1. 通过查询节点列表，测试 Kubernetes 是否能正常连接；
     * 2. 通过查询 Agent 镜像版本，测试镜像仓库是否能正常连接；
     */
    @Override
    protected void testAgentConnection(ServerLoginParam serverLoginParam) {
        try {
            log.info("start testing the Kubernetes connection:[{}]", serverLoginParam.getIp());
            ApiClient client = ClientBuilder.kubeconfig(
                    KubeConfig.loadKubeConfig(new StringReader(serverLoginParam.getKubeConfig()))
            ).build();
            CoreV1Api api = new CoreV1Api(client);
            V1NodeList nodeList = api.listNode();
            log.info("Kubernetes connect successfully：{}", nodeList);
        } catch (Exception e) {
            log.error("Kubernetes connect exception：" + e.getMessage());
            log.debug("", e);
            throw new ApiRequestException(ApiRequestErrorCode.AGENT_K8S_ERROR);
        }

        try {
            // 如果没有传密码，则使用原密码
            if (StringUtils.isBlank(serverLoginParam.getRegistryPassword())) {
                AntServerParam antServerParam = this.antConfigService.getServerParam(serverLoginParam.getIp());
                if (antServerParam != null && antServerParam.getK8s() != null) {
                    serverLoginParam.setRegistryPassword(antServerParam.getK8s().getRegistryPassword());
                }
            } else {
                serverLoginParam.setRegistryPassword(managerEncryptor.encrypt(serverLoginParam.getRegistryPassword()));
            }
            boolean checkDockerLogin = DockerLoginCheckerUtils.checkDockerLoginWithProtocolSupport(serverLoginParam.getRegistryUrl(), serverLoginParam.getRegistryUsername(), managerEncryptor.decrypt(serverLoginParam.getRegistryPassword()));
            if (!checkDockerLogin) {
                log.error("docker login failed, registryUrl:{}, username:{}", serverLoginParam.getRegistryUrl(), serverLoginParam.getRegistryUsername());
                throw new ApiRequestException(ApiRequestErrorCode.AGENT_REGISTRY_ERROR);
            }
            log.info("start the mirror warehouse connection test:[{}]", serverLoginParam.getRegistryUrl());
            AgentVersionDto versionDto = null;
            try (KubernetesDeployClient deployClient = new KubernetesDeployClient(serverLoginParam, appContext.getSkynetHome(), baseAuthRestTemplateBuilder, managerEncryptor, managerProperties)) {
                versionDto = deployClient.getRemoteVersion();
            } catch (HttpClientErrorException.NotFound e) {
                // ignore 404 not found
            }
            log.info("the mirror warehouse is connected successfully：{}", versionDto);

            if (StringUtils.isNotBlank(serverLoginParam.getRegistryPassword())) {
                serverLoginParam.setRegistryPassword(managerEncryptor.decrypt(serverLoginParam.getRegistryPassword()));
            }
        } catch (Exception e) {
            log.error("the mirror warehouse is connected exception：" + e.getMessage());
            log.debug("", e);
            throw new ApiRequestException(ApiRequestErrorCode.AGENT_REGISTRY_ERROR);
        }
    }

    /**
     * 将 agent 包部署到 Kubernetes
     */
    @Override
    protected void doDeployAgent(String ip, boolean dockerEnabled, boolean isForce) throws Exception {
        log.info("Deploy agent ip={}", ip);
        AntServerParam antServerParam = this.antConfigService.getServerParam(ip);
        if (antServerParam == null) {
            throw new AgentNotRegisterException(ip);
        }
        Assert.isTrue(antServerParam.getK8s() != null, "antServerParam.k8s is null");
        try (KubernetesDeployClient deployClient = new KubernetesDeployClient(antServerParam.getK8s(), appContext.getSkynetHome(), baseAuthRestTemplateBuilder, managerEncryptor, managerProperties)) {
            deployClient.deploy(dockerEnabled, isForce);
        }
    }

    /**
     * 保存 agent 信息
     */
    @Override
    protected AntServerParam formatAntServerParam(AntServerParam antServerParam, ServerLoginParam serverLoginParam) throws Exception {
        if (antServerParam == null) {
            // 新注册，对密码内容进行加密
            antServerParam = new AntServerParam();
            antServerParam.setIp(serverLoginParam.getIp());
            if (StringUtils.isNotBlank(serverLoginParam.getRegistryPassword())) {
                serverLoginParam.setRegistryPassword(managerEncryptor.encrypt(serverLoginParam.getRegistryPassword()));
            }
        } else {
            // 修改已注册的节点信息，如果密码为空则使用原密码，不为空则对密码进行加密
            if (StringUtils.isBlank(serverLoginParam.getRegistryPassword())) {
                serverLoginParam.setRegistryPassword(antServerParam.getK8s().getRegistryPassword());
            } else {
                serverLoginParam.setRegistryPassword(managerEncryptor.encrypt(serverLoginParam.getRegistryPassword()));
            }
        }
        
        if(StringUtils.isNotBlank(managerProperties.getRegistryContextPath())) {
            serverLoginParam.setRegistryContextPath(managerProperties.getRegistryContextPath());
        }

        antServerParam.setType(AgentType.KUBERNETES);
        antServerParam.setK8s(serverLoginParam);
        if (k8sConfigProperties.isUseInternalIp()) {
            Map<String, Object> serverInfo = getServerInfo(antServerParam);
            if (serverInfo.get("ip.internal") != null) {
                antServerParam.setIp(serverInfo.get("ip.internal").toString());
            }
        }
        return antServerParam;
    }

    /**
     * 获取 agent 版本
     */
    @Override
    protected AgentVersionDto getAgentVersion(AgentDto agentDto) {
        AntServerParam antServerParam = this.antConfigService.getServerParam(agentDto.getIp());
        if (antServerParam == null) {
            throw new AgentNotRegisterException(agentDto.getIp());
        }
        Assert.isTrue(antServerParam.getK8s() != null, "antServerParam.k8s is null");
        try (KubernetesDeployClient deployClient = new KubernetesDeployClient(antServerParam.getK8s(), appContext.getSkynetHome(), baseAuthRestTemplateBuilder, managerEncryptor, managerProperties)) {
            return deployClient.getRemoteVersion();
        } catch (Exception e) {
            log.error("Get agent version error.[{}:{} by user={} error:{}]", agentDto.getIp(), agentDto.getSshPort(), agentDto.getSshUser(), e.getMessage());
        }
        return null;
    }

    /**
     * 获取 agent 所在服务器信息
     */
    @Override
    protected Map<String, Object> getServerInfo(AntServerParam antServerParam) {

        Map<String, Object> serverInfo = agentServerInfoCache.getIfPresent(antServerParam.getIp());
        if (serverInfo != null) {
            return serverInfo;
        }

        try {
            serverInfo = getServerInfoByKubernetes(antServerParam);
            refreshServerInfo(antServerParam, serverInfo);
            agentServerInfoCache.put(antServerParam.getIp(), serverInfo);
            return serverInfo;
        } catch (Exception e) {
            log.error("Get server info error." + e.getMessage());
            return Collections.emptyMap();
        }
    }

    /**
     * 调用 Kubernetes API 获取节点的服务器信息
     */
    private Map<String, Object> getServerInfoByKubernetes(AntServerParam antServerParam) throws Exception {

        Map<String, Object> serverInfo = new HashMap<>();
        KubeConfig kubeConfig = KubeConfig.loadKubeConfig(new StringReader(antServerParam.getK8s().getKubeConfig()));
        serverInfo.put("host", kubeConfig.getCurrentContext());
        serverInfo.put("ip", antServerParam.getIp());

        ApiClient client = ClientBuilder.kubeconfig(kubeConfig).build();
        CoreV1Api api = new CoreV1Api(client);
        V1NodeList nodeList = api.listNode();
        serverInfo.put("nodes", nodeList.getItems().size());
        if (nodeList.getItems().size() > 0) {
            serverInfo.put("os", "Kubernetes " + nodeList.getItems().get(0).getStatus().getNodeInfo().getKubeletVersion());
            serverInfo.put("ip.internal", getInternalIp(nodeList));
        }

        int cpu = 0;
        double mem = 0;
        for (V1Node node : nodeList.getItems()) {
            if (node.getStatus() != null && node.getStatus().getCapacity() != null) {
                BigDecimal cpuNumber = node.getStatus().getCapacity().get("cpu").getNumber();
                if (cpuNumber != null) {
                    cpu += cpuNumber.intValue();
                }
                BigDecimal memNumber = node.getStatus().getCapacity().get("memory").getNumber();
                if (memNumber != null) {
                    mem += memNumber.doubleValue() / 1024 / 1024 / 1024;
                }
            }
        }
        serverInfo.put("cpu", cpu);
        serverInfo.put("mem", (int) Math.ceil(mem));
        return serverInfo;
    }

    /**
     * 获取集群内部 IP
     * @param nodeList
     * @return
     */
    private String getInternalIp(V1NodeList nodeList) {
        if (nodeList == null || nodeList.getItems().isEmpty()) {
            return null;
        }
        V1Node node = nodeList.getItems().get(0);
        if (node != null && node.getStatus() != null && node.getStatus().getAddresses() != null) {
            for (V1NodeAddress address : node.getStatus().getAddresses()) {
                if ("InternalIP".equals(address.getType())) {
                    return address.getAddress();
                }
            }
        }
        return null;
    }

    /**
     * 更新 Zookeeper 中的服务器信息
     */
    private void refreshServerInfo(AntServerParam antServerParam, Map<String, Object> serverInfo) {
        AntServerProperty antServerProperty = new AntServerProperty();
        antServerProperty.setHost(String.valueOf(serverInfo.get("host")));
        antServerProperty.setIp(String.valueOf(serverInfo.get("ip")));
        antServerProperty.setOs(String.valueOf(serverInfo.get("os")));
        antServerProperty.setCpu((int) (serverInfo.get("cpu")));
        antServerProperty.setMem((int) (serverInfo.get("mem")));
        antServerParam.setSys(antServerProperty);
        this.antConfigService.setServerParam(antServerParam);
    }

    /**
     * 启动 agent
     */
    @Override
    public void startAgent(AntServerParam antServerParam) throws Exception {

        Map<String, Object> agentMap = getAgentMap(antServerParam);
        // start `metrics-server`
        k8sKubectlService.apply(antServerParam.getIp(), antServerParam.getK8s().getKubeConfig(), "metrics-server.yaml", agentMap);

        // start `skynet-agent`
        k8sKubectlService.apply(antServerParam.getIp(), antServerParam.getK8s().getKubeConfig(), "skynet-agent.yaml", agentMap);

        // start `skynet-operator`
        k8sKubectlService.apply(antServerParam.getIp(), antServerParam.getK8s().getKubeConfig(), "skynet-operator.yaml", agentMap);

        // create `skynet-init` configmap
        k8sKubectlService.apply(antServerParam.getIp(), antServerParam.getK8s().getKubeConfig(), "skynet-init.yaml", agentMap);

        // create dockerSecret, 包含服务所在命名空间， skynet-system
        k8sKubectlService.createDockerSecret(antServerParam.getIp(), antServerParam.getK8s().getKubeConfig(), managerProperties.getK8sNamespace(), antServerParam.getK8s().getRegistryUrl(), antServerParam.getK8s().getRegistryUsername(), managerEncryptor.decrypt(antServerParam.getK8s().getRegistryPassword()));
        k8sKubectlService.createDockerSecret(antServerParam.getIp(), antServerParam.getK8s().getKubeConfig(), "skynet-system", antServerParam.getK8s().getRegistryUrl(), antServerParam.getK8s().getRegistryUsername(), managerEncryptor.decrypt(antServerParam.getK8s().getRegistryPassword()));
        k8sKubectlService.createDockerSecret(antServerParam.getIp(), antServerParam.getK8s().getKubeConfig(), "kube-system", antServerParam.getK8s().getRegistryUrl(), antServerParam.getK8s().getRegistryUsername(), managerEncryptor.decrypt(antServerParam.getK8s().getRegistryPassword()));

    }

    /**
     * 停止 agent
     */
    @Override
    public void stopAgent(AntServerParam antServerParam, boolean stopAction) throws Exception {

        Map<String, Object> agentMap = getAgentMap(antServerParam);

        // stop `skynet-agent`
        k8sKubectlService.delete(antServerParam.getIp(), antServerParam.getK8s().getKubeConfig(), "skynet-agent.yaml", agentMap);

        if (stopAction) {
            // 删除docker密钥
            k8sKubectlService.deleteDockerSecret(antServerParam.getIp(), antServerParam.getK8s().getKubeConfig(), managerProperties.getK8sNamespace());
            k8sKubectlService.deleteDockerSecret(antServerParam.getIp(), antServerParam.getK8s().getKubeConfig(), "skynet-system");
            k8sKubectlService.deleteDockerSecret(antServerParam.getIp(), antServerParam.getK8s().getKubeConfig(), "kube-system");
            
            // stop `metrics-server`
            k8sKubectlService.delete(antServerParam.getIp(), antServerParam.getK8s().getKubeConfig(), "metrics-server.yaml", agentMap);

            // stop `skynet-operator`
            // 删除 SkynetApps CRD 之后，所有服务会级联删除
            k8sKubectlService.delete(antServerParam.getIp(), antServerParam.getK8s().getKubeConfig(), "skynet-operator.yaml", agentMap);

            // stop `skynet-init`
            k8sKubectlService.delete(antServerParam.getIp(), antServerParam.getK8s().getKubeConfig(), "skynet-init.yaml", agentMap);
        }
    }

    /**
     * 组装 agent 配置
     */
    private Map<String, Object> getAgentMap(AntServerParam antServerParam) {
        Map<String, Object> agentMap = new HashMap<>();
        // *************
        agentMap.put("agent.ip", antServerParam.getIp());
        // 32630
        agentMap.put("agent.port", managerProperties.getAgentK8sPort());
        // 3.4.2
        agentMap.put("agent.project-version", appVersionBuilder.getAgentVersion().get("project-version"));
        // 1011
        agentMap.put("agent.build-sid", appVersionBuilder.getAgentVersion().get("build-sid"));
        // cfb3c2bf
        agentMap.put("agent.build-number", appVersionBuilder.getAgentVersion().get("build-number"));
        // 20221125
        agentMap.put("agent.build-time", appVersionBuilder.getAgentVersion().get("build-time"));
        // master
        agentMap.put("agent.build-branch", appVersionBuilder.getAgentVersion().get("build-branch"));
        // 4.0.5
        agentMap.put("agent.skynet-boot-version", appVersionBuilder.getAgentVersion().get("skynet-boot-version"));
        // ************:2181
        agentMap.put("zookeeper.server-list", antConfigService.getSkynetZKProperties().getServerList());
        // skynet
        agentMap.put("zookeeper.cluster-name", antConfigService.getSkynetZKProperties().getClusterName());
        // http://************:2230
        agentMap.put("skynet.manager.url", appContext.getUri());
        // /iflytek/server/skynet
        agentMap.put("skynet.home", appContext.getSkynetHome());
        // skynet
        agentMap.put("skynet.auth.api-key", authClientProperties.getApiKey());
        // SKYNET_API_SECRET_PLACEHOLDER
        agentMap.put("skynet.auth.api-secret", authClientProperties.getApiSecret());
        // k8s namespace default
        agentMap.put("skynet.k8s.namespace", managerProperties.getK8sNamespace());
        // *************:5000
        agentMap.put("registry.url", antServerParam.getK8s().getRegistryUrl());
        //hy-docker-private/
        agentMap.put("registry.context-path", managerProperties.getRegistryContextPath().trim());
        // admin
        agentMap.put("registry.username", antServerParam.getK8s().getRegistryUsername());
        // passw0rd
        agentMap.put("registry.password", managerEncryptor.decrypt(antServerParam.getK8s().getRegistryPassword()));
        return agentMap;
    }
}

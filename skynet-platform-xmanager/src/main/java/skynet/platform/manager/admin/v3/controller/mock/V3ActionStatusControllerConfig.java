//package skynet.platform.manager.admin.v3.controller.mock;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Profile;
//import skynet.platform.feign.model.ActionStatusDto;
//import skynet.platform.feign.model.NoDataResponse;
//import skynet.platform.manager.admin.v3.controller.V3ActionStatusController;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.anyString;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.when;
//
//@Profile("mock")
//@Configuration
//public class V3ActionStatusControllerConfig {
//
//    @Bean("MockV3ActionStatusController")
//    public V3ActionStatusController newMock() {
//        V3ActionStatusController ret = mock(V3ActionStatusController.class);
//        try {
//            when(ret.getAllStatus(anyString())).thenReturn(getAllStatus());
//            when(ret.getStatus(anyString(), anyString())).thenReturn(getStatus());
//            when(ret.updateInstanceStatus(any())).thenReturn(updateInstanceStatus());
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return ret;
//    }
//
//    private V3ActionStatusController.GetAllStatusResponse getAllStatus() {
//        return mockGetAllStatusResponse();
//    }
//
//    private V3ActionStatusController.GetAllStatusResponse getStatus() {
//        return mockGetAllStatusResponse();
//    }
//
//    private V3ActionStatusController.GetAllStatusResponse mockGetAllStatusResponse() {
//        V3ActionStatusController.GetAllStatusResponse response = new V3ActionStatusController.GetAllStatusResponse();
//        response.setCode(0);
//        response.setMessage("mock");
//        List<ActionStatusDto> data = new ArrayList<>();
//        ActionStatusDto actionStatusDto = new ActionStatusDto();
//        actionStatusDto.setIp("127.0.0.1");
//        actionStatusDto.setAgentPort(6230);
//        actionStatusDto.setActionName("rest-test-v1@turing-test");
//        actionStatusDto.setActionPoint("rest-test-v1@turing-test");
//        actionStatusDto.setActionID("rest-test-v1@turing-test");
//        actionStatusDto.setPluginCode("turing-test");
//        actionStatusDto.setPluginName("test system");
//        actionStatusDto.setPort(8080);
//        actionStatusDto.setPid(1233);
//        actionStatusDto.setHomePageURL("/index");
//        actionStatusDto.setStartTime("02:00:01");
//        actionStatusDto.setUpTime("02:00:01");
//        actionStatusDto.setEnabled(true);
//        actionStatusDto.setStatus("UP");
//        actionStatusDto.setStartupOrder(10);
//        actionStatusDto.setStartupOrder(-9999);
//        data.add(actionStatusDto);
//        response.setData(data);
//        return response;
//    }
//
//    private NoDataResponse updateInstanceStatus() {
//        NoDataResponse noDataResponse = new NoDataResponse();
//        noDataResponse.setCode(0);
//        noDataResponse.setMessage("mock");
//        return noDataResponse;
//    }
//
//}

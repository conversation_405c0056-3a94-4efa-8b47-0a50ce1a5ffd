package skynet.platform.manager.admin.service;

import org.springframework.util.Assert;
import skynet.platform.common.exception.AntException;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.NodeDescription;
import skynet.platform.manager.admin.config.ManagerProperties;

/**
 * <AUTHOR>
 */
public class BaseService {

    protected final ManagerProperties managerProperties;
    private final IAntConfigService antConfigService;

    public BaseService(ManagerProperties managerProperties, IAntConfigService antConfigService) {
        this.managerProperties = managerProperties;
        this.antConfigService = antConfigService;
    }

    public NodeDescription checkAction(String plugin, String action) {
        Assert.hasText(plugin, "the plugin is blank.");
        Assert.hasText(action, "the action is blank.");
        plugin = plugin.trim();
        action = action.trim();
        NodeDescription actionNodeDescription = this.antConfigService.getAction(plugin, action);
        if (actionNodeDescription == null) {
            throw new AntException(String.format("the action [%s@%s] not exist", action, plugin));
        }
        return actionNodeDescription;
    }

    public NodeDescription checkPlugin(String plugin) {
        Assert.hasText(plugin, "the plugin is blank.");
        plugin = plugin.trim();
        NodeDescription pluginNode = this.antConfigService.getPlugin(plugin);
        if (pluginNode == null) {
            throw new AntException("the plugin [%s] not exist.", plugin);
        }
        return pluginNode;
    }

}

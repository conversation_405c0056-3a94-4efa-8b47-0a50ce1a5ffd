package skynet.platform.manager.admin.v3.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.NoDataResponse;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.model.SwitchLabelTypeDto;
import skynet.platform.feign.service.V3SwitchLabelType;
import skynet.platform.manager.admin.v3.service.V3SwitchLabelTypeService;
import skynet.platform.manager.annotation.ConditionalOnApiV3Enabled;
import skynet.platform.manager.audit.annotation.AuditLog;

import java.util.List;

@Profile("!mock")
@Slf4j
@RestController
@ExposeSwagger2
@ConditionalOnApiV3Enabled
public class V3SwitchLabelTypeController implements V3SwitchLabelType {

    private final V3SwitchLabelTypeService v3SwitchLabelTypeService;

    public V3SwitchLabelTypeController(V3SwitchLabelTypeService v3SwitchLabelTypeService) {
        this.v3SwitchLabelTypeService = v3SwitchLabelTypeService;
    }

    @Override
    public SkynetApiResponse<List<SwitchLabelTypeDto>> getAll() {
        SkynetApiResponse<List<SwitchLabelTypeDto>> resp;
        List<SwitchLabelTypeDto> data = v3SwitchLabelTypeService.getAll();
        try {
            resp = SkynetApiResponse.success(data);
        } catch (Exception e) {
            log.error("", e);
            resp = SkynetApiResponse.fail(e);
        }
        return resp;
    }

    @Override
    @AuditLog(module = "服务功能开关项类型管理", operation = "新增开关选项类型", message = "request=#{#request}")
    public NoDataResponse create(@RequestBody SwitchLabelTypeDto request) {
        return new NoDataResponse();
    }

    @Override
    @AuditLog(module = "服务功能开关项类型管理", operation = "更新开关选项类型", message = "code=#{#code},request=#{#request}")
    public NoDataResponse create(@PathVariable String code,
                                 @RequestBody SwitchLabelTypeDto request) {
        return new NoDataResponse();
    }

    @Override
    @AuditLog(module = "服务功能开关项类型管理", operation = "删除开关选项类型", message = "code=#{#code}")
    public NoDataResponse create(@PathVariable String code) {
        return new NoDataResponse();
    }

}

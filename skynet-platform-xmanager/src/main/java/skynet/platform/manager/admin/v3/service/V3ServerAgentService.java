package skynet.platform.manager.admin.v3.service;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import lombok.extern.slf4j.Slf4j;
import skynet.boot.AppContext;
import skynet.boot.security.SkynetEncryption;
import skynet.platform.common.domain.AntServerParam;
import skynet.platform.common.domain.AntServerProperty;
import skynet.platform.common.domain.ServerLoginParam;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;
import skynet.platform.feign.model.AgentDto;
import skynet.platform.feign.model.AgentType;
import skynet.platform.feign.model.AgentVersionDto;
import skynet.platform.manager.admin.config.ManagerProperties;
import skynet.platform.manager.admin.service.ServerService;
import skynet.platform.common.auth.ManagerEncryptor;
import skynet.platform.manager.admin.service.deploy.SkynetDeployClient;

/**
 * 服务器节点管理
 */
@Slf4j
@Service
public class V3ServerAgentService extends V3BaseAgentService {

    private final ServerService serverService;
    private final AppContext appContext;
    
    public V3ServerAgentService(ServerService serverService, AppContext appContext,
                                ManagerEncryptor managerEncryptor, SkynetEncryption skynetEncryption, IAntConfigService antConfigService, ManagerProperties managerProperties) {
        super(managerEncryptor, skynetEncryption, antConfigService, managerProperties);
        this.serverService = serverService;
        this.appContext = appContext;
    }
    
    /**
     * 校验 AgentDto 参数格式
     */
    @Override
    protected void checkAgentDto(AgentDto agentDto) {
        if (StringUtils.isBlank(agentDto.getIp()) || agentDto.getSshPort() <= 0 || StringUtils.isBlank(agentDto.getSshUser())) {
            throw new ApiRequestException(ApiRequestErrorCode.PARAM_ILLEGAL);
        }
    }

    /**
     * 测试是否连接服务器成功
     */
    @Override
    protected void testAgentConnection(ServerLoginParam serverLoginParam) {
        serverService.testConnectServer(serverLoginParam);
    }

    /**
     * 将 agent 包部署到服务器
     */
    @Override
    public void doDeployAgent(String ip, boolean dockerEnabled, boolean isForce) throws Exception {
        ServerLoginParam serverLoginParam = serverService.getServerLoginParam(ip);
        Assert.isTrue(serverLoginParam != null, "serverLoginParam is null");
        Assert.isTrue(serverLoginParam.getPort() > 0, "the port is invalid.");
        try (SkynetDeployClient deployClient = new SkynetDeployClient(serverLoginParam, appContext.getSkynetHome())) {
            deployClient.deploy(dockerEnabled, isForce);
        }
    }

    /**
     * 保存 agent 信息
     */
    @Override
    protected AntServerParam formatAntServerParam(AntServerParam antServerParam, ServerLoginParam serverLoginParam) throws Exception {
        if (antServerParam == null) {
            // 新注册
            antServerParam = new AntServerParam();
            antServerParam.setIp(serverLoginParam.getIp());
            if (StringUtils.isBlank(serverLoginParam.getPwd())) {
                throw new IllegalArgumentException("The password is Blank.");
            }
            //新注册的节点，密码使用加密过的提交的表单域
            serverLoginParam.setPwd(managerEncryptor.encrypt(serverLoginParam.getPwd()));
        } else {
            if (StringUtils.isBlank(serverLoginParam.getPwd())) {
                // 修改已注册的节点信息，如果密码表单域为空则使用原密码
                serverLoginParam.setPwd(antServerParam.getSsh().getPwd());
            } else {
                // 修改已注册的节点信息，如果密码表单域域不为空则使用加密过的密码
                serverLoginParam.setPwd(managerEncryptor.encrypt(serverLoginParam.getPwd()));
            }
        }
        antServerParam.setType(AgentType.SERVER);
        antServerParam.setSsh(serverLoginParam);
        return antServerParam;
    }

    /**
     * 获取 agent 版本
     */
    @Override
    protected AgentVersionDto getAgentVersion(AgentDto agentDto) {
        ServerLoginParam serverLoginParam = serverService.getServerLoginParam(agentDto.getIp());
        try (SkynetDeployClient deployClient = new SkynetDeployClient(serverLoginParam, appContext.getSkynetHome())) {
            return deployClient.getRemoteVersion();
        } catch (Exception e) {
            log.error("Get agent version error.[{}:{} by user={} error:{}]", agentDto.getIp(), agentDto.getSshPort(), agentDto.getSshUser(), e.getMessage());
        }
        return null;
    }

    /**
     * 获取 agent 所在的服务器信息
     */
    @Override
    protected Map<String, Object> getServerInfo(AntServerParam server) {
        Map<String, Object> serverInfo = new HashMap<>();
        AntServerProperty antServerProperty = server.getSys();
        if (antServerProperty != null) {
            serverInfo.put("ip", antServerProperty.getIp());
            serverInfo.put("host", antServerProperty.getHost());
            serverInfo.put("os", antServerProperty.getOs());
            serverInfo.put("cpu", antServerProperty.getCpu());
            serverInfo.put("mem", antServerProperty.getMem());
            serverInfo.put("gpu", antServerProperty.getGpu());
        }
        return serverInfo;
    }

    /**
     * 启动 agent
     */
    @Override
    public void startAgent(AntServerParam antServerParam) throws Exception {
        serverService.startServerByIpList(Collections.singletonList(antServerParam.getIp()));
    }

    /**
     * 停止 agent
     */
    @Override
    public void stopAgent(AntServerParam antServerParam, boolean stopAction) throws Exception {
        serverService.stopServerByIpList(Collections.singletonList(antServerParam.getIp()), stopAction);
    }
}

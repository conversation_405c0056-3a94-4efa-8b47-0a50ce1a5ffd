//package skynet.platform.manager.admin.v3.controller.mock;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Profile;
//import skynet.platform.feign.model.ClusterInfoDto;
//import skynet.platform.feign.model.SkynetApiResponse;
//import skynet.platform.manager.admin.v3.controller.V3ClusterController;
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.when;
//
//@Profile("mock")
//@Configuration
//public class V3ClusterControllerConfig {
//
//    @Bean("MockV3ClusterController")
//    public V3ClusterController newMock() {
//        V3ClusterController ret = mock(V3ClusterController.class);
//        try {
////            when(ret.getInfo()).thenReturn(this.getInfo());
//            when(ret.getProperties()).thenReturn(this.getProperties());
//            when(ret.getLoggingLevels()).thenReturn(this.getLoggingLevels());
//            when(ret.updateProperties(any())).thenReturn(this.updatePropertiesLevels());
//            when(ret.updateLoggingLevels(any())).thenReturn(this.updateLoggingLevels());
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return ret;
//    }
//
//    private V3ClusterController.GetClusterInfoResponse getInfo() {
//        V3ClusterController.GetClusterInfoResponse ret = new V3ClusterController.GetClusterInfoResponse();
//        ClusterInfoDto data = new ClusterInfoDto();
//        data.setClusterName("skynet");
//        data.setSkynetHome("/iflytek/server/skynet");
//        data.setZkServers("192.168.31.123");
//        ret.setData(data);
//        return ret;
//    }
//
//    private SkynetApiResponse<String> getProperties() {
//        SkynetApiResponse<String> response = new SkynetApiResponse<>();
//        response.setCode(0);
//        response.setMessage("mock");
//        response.setData("turing.properties.one=one\n" +
//                "turing.properties.two=two");
//        return response;
//    }
//
//    private SkynetApiResponse<String> getLoggingLevels() {
//        SkynetApiResponse<String> response = new SkynetApiResponse<>();
//        response.setCode(0);
//        response.setMessage("mock");
//        response.setData("skynet.boot=INFO");
//        return response;
//    }
//
//    private SkynetApiResponse<String> updatePropertiesLevels() {
//        SkynetApiResponse<String> response = new SkynetApiResponse<>();
//        response.setCode(0);
//        response.setMessage("mock");
//        response.setData("turing.properties.one=one");
//        return response;
//    }
//
//    private SkynetApiResponse<String> updateLoggingLevels() {
//        SkynetApiResponse<String> response = new SkynetApiResponse<>();
//        response.setCode(0);
//        response.setMessage("mock");
//        response.setData("skynet.boot=INFO\n" +
//                "com.iflytek=INFO");
//        return response;
//    }
//
//}

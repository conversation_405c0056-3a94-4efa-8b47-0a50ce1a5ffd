package skynet.platform.manager.k8s.controller;

import java.util.List;

import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSONObject;

import lombok.extern.slf4j.Slf4j;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3K8sNodeMetric;
import skynet.platform.manager.k8s.service.K8sNodeMetricService;

@Slf4j
@RestController
@ExposeSwagger2
public class K8sNodeMetricController implements V3K8sNodeMetric {
    
    private final K8sNodeMetricService k8sNodeMetricService;

    public K8sNodeMetricController(K8sNodeMetricService k8sNodeMetricService) {
        this.k8sNodeMetricService = k8sNodeMetricService;
    }

    @Override
    public SkynetApiResponse<List<JSONObject>> getNodeMetrics(String ip, K8sQuery k8sQuery) throws Exception {
        SkynetApiResponse<List<JSONObject>> response = new SkynetApiResponse<>();
        List<JSONObject> results = k8sNodeMetricService.getNodeMetrics(ip, k8sQuery);
        response.setData(results);
        log.debug("getNodeMetrics response:{}", response);
        return response;
    }

    @Override
    public SkynetApiResponse<JSONObject> getNodeMetric(String ip, String nodeName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sNodeMetricService.getNodeMetric(ip, nodeName);
        response.setData(result);
        log.debug("getNodeMetric response:{}", response);
        return response;
    }
    
}

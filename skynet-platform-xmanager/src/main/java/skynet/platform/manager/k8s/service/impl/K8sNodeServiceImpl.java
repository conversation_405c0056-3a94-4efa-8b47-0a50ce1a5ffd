package skynet.platform.manager.k8s.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.gson.Gson;
import io.kubernetes.client.custom.V1Patch;
import io.kubernetes.client.extended.kubectl.Kubectl;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.models.V1Node;
import io.kubernetes.client.openapi.models.V1NodeList;
import io.kubernetes.client.openapi.models.V1Status;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.manager.admin.v3.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sNodeService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Kubernetes Node 服务实现类
 * <p>
 * 升级说明：
 * - 兼容 kubernetes-client-java 24.0.0 版本
 * - 更新了 Node 标签和注解的修改方式，使用 JSON Patch 替代过时的 read-modify-write 模式
 * - 保持 Kubectl 扩展功能的兼容性
 * - 添加了详细的方法注释和升级说明
 */
@Slf4j
@Service
public class K8sNodeServiceImpl extends K8sBaseService implements K8sNodeService {

    public K8sNodeServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 Node 列表
     */
    @Override
    public List<JSONObject> getNodes(String ip, K8sQuery k8sQuery) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        List<JSONObject> nodeDtoList = new ArrayList<>();
        V1NodeList nodeList = api.listNode().execute();

        // 应用 K8sQuery 过滤条件
        List<V1Node> filteredNodes = applyK8sQueryFilter(nodeList.getItems(), k8sQuery);

        for (V1Node node : filteredNodes) {
            nodeDtoList.add(toJSON(node));
        }
        return nodeDtoList;
    }

    /**
     * 获取 Node 详情
     */
    @Override
    public JSONObject getNode(String ip, String nodeName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        V1Node node = api.readNode(nodeName).execute();
        return toJSON(node);
    }

    /**
     * 获取 Node Yaml
     */
    @Override
    public String getNodeYaml(String ip, String nodeName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        V1Node node = api.readNode(nodeName).execute();
        return Yaml.dump(node);
    }

    /**
     * 更新 Node
     */
    @Override
    public JSONObject updateNode(String ip, String nodeName, JSONObject node) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        Gson gson = new Gson();
        V1Node body = gson.fromJson(node.toString(), V1Node.class);
        if (body.getMetadata() != null) {
            body.getMetadata().setResourceVersion(null);
        }
        V1Node result = api.replaceNode(nodeName, body).execute();
        return toJSON(result);
    }

    /**
     * 删除 Node
     */
    @Override
    public JSONObject deleteNode(String ip, String nodeName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        V1Status status = api.deleteNode(nodeName).execute();
        return toJSON(status);
    }

    /**
     * 更新 Node 标签
     * <p>
     * 升级说明：
     * - 替换过时的 read-modify-write 模式为 JSON Patch 方式
     * - 使用 patchNode 方法替代 replaceNode，避免并发冲突
     * - 在 24.0.0 版本中推荐使用 patch 操作进行资源更新
     *
     * @param ip       集群节点IP地址
     * @param nodeName 节点名称
     * @param labels   要设置的标签映射
     * @return 更新后的 Node 对象
     * @throws Exception 当更新失败时抛出
     */
    @Override
    public JSONObject updateNodeLabels(String ip, String nodeName, Map<String, String> labels) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        // 使用 JSON Patch 方式更新标签，避免并发冲突
        JSONArray patch = new JSONArray();
        patch.add(new JSONObject()
                .fluentPut("op", "replace")
                .fluentPut("path", "/metadata/labels")
                .fluentPut("value", labels));

        V1Patch patchBody = new V1Patch(patch.toString());
        V1Node result = api.patchNode(nodeName, patchBody).execute();
        return toJSON(result);
    }

    /**
     * 更新 Node 注解
     * <p>
     * 升级说明：
     * - 替换过时的 read-modify-write 模式为 JSON Patch 方式
     * - 使用 patchNode 方法替代 replaceNode，避免并发冲突
     * - 在 24.0.0 版本中推荐使用 patch 操作进行资源更新
     *
     * @param ip          集群节点IP地址
     * @param nodeName    节点名称
     * @param annotations 要设置的注解映射
     * @return 更新后的 Node 对象
     * @throws Exception 当更新失败时抛出
     */
    @Override
    public JSONObject updateNodeAnnotations(String ip, String nodeName, Map<String, String> annotations) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        // 使用 JSON Patch 方式更新注解，避免并发冲突
        JSONArray patch = new JSONArray();
        patch.add(new JSONObject()
                .fluentPut("op", "replace")
                .fluentPut("path", "/metadata/annotations")
                .fluentPut("value", annotations));

        V1Patch patchBody = new V1Patch(patch.toString());
        V1Node result = api.patchNode(nodeName, patchBody).execute();
        return toJSON(result);
    }

    /**
     * 暂停调度
     */
    @Override
    public JSONObject cordonNode(String ip, String nodeName) throws Exception {
        V1Node result = Kubectl.cordon()
                .apiClient(initApiClient(ip))
                .name(nodeName)
                .execute();
        return toJSON(result);
    }

    /**
     * 恢复调度
     */
    @Override
    public JSONObject uncordonNode(String ip, String nodeName) throws Exception {
        V1Node result = Kubectl.uncordon()
                .apiClient(initApiClient(ip))
                .name(nodeName)
                .execute();
        return toJSON(result);
    }

    /**
     * 排空节点
     */
    @Override
    public JSONObject drainNode(String ip, String nodeName) throws Exception {
        V1Node result = Kubectl.drain()
                .apiClient(initApiClient(ip))
                .name(nodeName)
                .execute();
        return toJSON(result);
    }
}

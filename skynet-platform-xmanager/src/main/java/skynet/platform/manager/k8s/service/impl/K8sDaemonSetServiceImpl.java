package skynet.platform.manager.k8s.service.impl;

import com.alibaba.fastjson2.JSONObject;
import io.kubernetes.client.custom.V1Patch;
import io.kubernetes.client.openapi.apis.AppsV1Api;
import io.kubernetes.client.openapi.models.V1DaemonSet;
import io.kubernetes.client.openapi.models.V1DaemonSetList;
import io.kubernetes.client.openapi.models.V1Status;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.K8sUpdateDaemonSetStrategy;
import skynet.platform.feign.model.K8sUpdateImage;
import skynet.platform.manager.admin.v3.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sDaemonSetService;

import java.util.ArrayList;
import java.util.List;

/**
 * Kubernetes DaemonSet 服务实现类
 * <p>
 * 升级说明：
 * - 兼容 kubernetes-client-java 24.0.0 版本
 * - 移除了已弃用的 PatchUtils.patch 方法，改为直接调用 API
 * - 更新了所有 patch 操作以使用新的 API 模式
 * - 保持原有功能的完整性和兼容性
 */
@Slf4j
@Service
public class K8sDaemonSetServiceImpl extends K8sWorkloadService implements K8sDaemonSetService {

    public K8sDaemonSetServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 DaemonSet 列表
     */
    @Override
    public List<JSONObject> getDaemonSets(String ip, K8sQuery k8sQuery) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        List<JSONObject> daemonSetDtoList = new ArrayList<>();
        V1DaemonSetList daemonSetList;
        if (StringUtils.isNotBlank(k8sQuery.getNamespace())) {
            daemonSetList = api.listNamespacedDaemonSet(k8sQuery.getNamespace()).execute();
        } else {
            daemonSetList = api.listDaemonSetForAllNamespaces().execute();
        }
        // 应用 K8sQuery 过滤条件
        List<V1DaemonSet> filteredDaemonSets = applyK8sQueryFilter(daemonSetList.getItems(), k8sQuery);

        for (V1DaemonSet daemonSet : filteredDaemonSets) {
            daemonSetDtoList.add(toJSON(daemonSet));
        }
        return daemonSetDtoList;
    }

    /**
     * 获取 DaemonSet 详情
     */
    @Override
    public JSONObject getDaemonSet(String ip, String namespace, String daemonSetName) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        V1DaemonSet daemonSet = api.readNamespacedDaemonSet(daemonSetName, namespace).execute();
        return toJSON(daemonSet);
    }

    /**
     * 获取 DaemonSet Yaml
     */
    @Override
    public String getDaemonSetYaml(String ip, String namespace, String daemonSetName) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        V1DaemonSet daemonSet = api.readNamespacedDaemonSet(daemonSetName, namespace).execute();
        return Yaml.dump(daemonSet);
    }

    /**
     * 删除 DaemonSet
     */
    @Override
    public JSONObject deleteDaemonSet(String ip, String namespace, String daemonSetName) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        V1Status status = api.deleteNamespacedDaemonSet(daemonSetName, namespace).execute();
        return toJSON(status);
    }

    /**
     * 重启
     * <p>
     * 升级说明：
     * - 移除已弃用的 PatchUtils.patch 方法
     * - 直接使用 AppsV1Api.patchNamespacedDaemonSet 方法
     * - 在 24.0.0 版本中推荐直接调用 API 方法
     *
     * @param ip            集群节点IP地址
     * @param namespace     命名空间
     * @param daemonSetName DaemonSet 名称
     * @return 重启后的 DaemonSet 对象
     * @throws Exception 当重启失败时抛出
     */
    @Override
    public JSONObject restartDaemonSet(String ip, String namespace, String daemonSetName) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        // strategic-merge-patch a daemonset
        V1Patch patch = buildRestartPatch();
        V1DaemonSet daemonSet = api.patchNamespacedDaemonSet(daemonSetName, namespace, patch).execute();
        return toJSON(daemonSet);
    }

    /**
     * 调整镜像版本
     * <p>
     * 升级说明：
     * - 移除已弃用的 PatchUtils.patch 方法
     * - 直接使用 AppsV1Api.patchNamespacedDaemonSet 方法
     * - 在 24.0.0 版本中推荐直接调用 API 方法
     *
     * @param ip             集群节点IP地址
     * @param namespace      命名空间
     * @param daemonSetName  DaemonSet 名称
     * @param k8sUpdateImage 镜像更新信息
     * @return 更新后的 DaemonSet 对象
     * @throws Exception 当更新失败时抛出
     */
    @Override
    public JSONObject updateDaemonSetImages(String ip, String namespace, String daemonSetName, K8sUpdateImage k8sUpdateImage) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        // json-patch a daemonset
        V1Patch patch = buildUpdateImagePatch(k8sUpdateImage);
        V1DaemonSet daemonSet = api.patchNamespacedDaemonSet(daemonSetName, namespace, patch).execute();
        return toJSON(daemonSet);
    }

    /**
     * 修改更新策略
     * <p>
     * 升级说明：
     * - 移除已弃用的 PatchUtils.patch 方法
     * - 直接使用 AppsV1Api.patchNamespacedDaemonSet 方法
     * - 在 24.0.0 版本中推荐直接调用 API 方法
     * - 保持对 RollingUpdate 策略中百分比和整数值的支持
     *
     * @param ip                         集群节点IP地址
     * @param namespace                  命名空间
     * @param daemonSetName              DaemonSet 名称
     * @param k8sUpdateDaemonSetStrategy 更新策略信息
     * @return 更新后的 DaemonSet 对象
     * @throws Exception 当更新失败时抛出
     */
    @Override
    public JSONObject updateDaemonSetStrategy(String ip, String namespace, String daemonSetName, K8sUpdateDaemonSetStrategy k8sUpdateDaemonSetStrategy) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        // 注意 rollingUpdate 的值可能是字符串型（20%），也可能是整型（3）
        JSONObject strategy = new JSONObject();
        strategy.put("type", k8sUpdateDaemonSetStrategy.getType());
        if ("RollingUpdate".equals(k8sUpdateDaemonSetStrategy.getType())) {
            JSONObject rollingUpdate = new JSONObject();
            rollingUpdate.put("maxUnavailable", getIntOrStr(k8sUpdateDaemonSetStrategy.getRollingUpdate().getMaxUnavailable()));
            strategy.put("rollingUpdate", rollingUpdate);
        }

        // json-patch a daemonset
        V1Patch patch = buildUpdateStrategyPatch(strategy);
        V1DaemonSet daemonSet = api.patchNamespacedDaemonSet(daemonSetName, namespace, patch).execute();
        return toJSON(daemonSet);
    }

}

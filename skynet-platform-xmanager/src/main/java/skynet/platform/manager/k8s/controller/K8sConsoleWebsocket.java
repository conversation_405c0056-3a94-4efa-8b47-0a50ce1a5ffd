package skynet.platform.manager.k8s.controller;


import com.alibaba.fastjson2.JSON;
import com.pty4j.PtyProcess;
import com.pty4j.PtyProcessBuilder;
import com.pty4j.WinSize;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import skynet.boot.websocket.HttpSessionConfigurator;
import skynet.platform.feign.model.AgentDto;
import skynet.platform.manager.admin.v3.service.V3AgentService;
import skynet.platform.manager.k8s.config.K8sConfigProperties;
import skynet.platform.manager.k8s.service.impl.K8sKubectlService;
import skynet.platform.manager.webshell.Constants;
import skynet.platform.manager.webshell.utils.ThreadPoolUtils;
import skynet.platform.manager.webshell.vo.WebShellData;

import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * console websocket
 * <p>
 * 请求场景：shell=bash
 * <p>
 * //指定 pod，namespace可选（默认为default），container可选（默认第一个）
 * /skynet/api/k8s/console/{ip}?namespace=namespace&podName=podName&container=container
 * <p>
 * //指定 node， 内部启动一个 pod，namespace可选（默认为default），container可选（默认第一个）
 * /skynet/api/k8s/console/{ip}?node=nodeName
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@ConditionalOnProperty(value = {"skynet.k8s.console.enabled"}, matchIfMissing = true)
@ServerEndpoint(value = "/skynet/api/k8s/console/{ip}", configurator = HttpSessionConfigurator.class)
public class K8sConsoleWebsocket {

    private final K8sConfigProperties k8sConfigProperties;

    private final K8sKubectlService k8sKubectlService;
    private final V3AgentService v3AgentService;
    private final Map<String, SessionContext> sessionIdMap = new ConcurrentHashMap<>();

    public K8sConsoleWebsocket(K8sConfigProperties k8sConfigProperties, K8sKubectlService k8sKubectlService, V3AgentService v3AgentService) {
        this.k8sConfigProperties = k8sConfigProperties;
        this.k8sKubectlService = k8sKubectlService;
        this.v3AgentService = v3AgentService;
    }

    @OnOpen
    public void onOpen(@PathParam("ip") String ip, Session session) {
        try {
            List<NameValuePair> pairs = URLEncodedUtils.parse(session.getQueryString(), StandardCharsets.UTF_8);
            SessionContext sessionContext = new SessionContext(ip, pairs);
            this.sessionIdMap.put(session.getId(), sessionContext);
        } catch (Exception e) {
            this.close(session, "connect to the remote shell failed.");
        }
        log.debug("Online console session size={}", sessionIdMap.size());
    }

    @OnMessage
    public void onMessage(String message, Session session) throws Exception {
        SessionContext sessionContext = sessionIdMap.get(session.getId());
        //调用service接收消息
        try {
            if (sessionContext == null) {
                throw new Exception("SessionContext Not Found.");
            }
            WebShellData shellData = JSON.parseObject(message, WebShellData.class);
            if (Constants.OPERATE_COMMAND.equals(shellData.getOperate())) {
                if (sessionContext.isProcessExits()) {
                    this.close(session, "the remote connection has been disconnected, please try again");
                    return;
                } else if (sessionContext.getPtySizeSum() != shellData.getChannelPtySizeSum()) {
                    //缓存 pty设置，前端发生修改时，再进行重新设置
                    WinSize winSize = new WinSize(shellData.getCol(), shellData.getRow());
                    sessionContext.getProcess().setWinSize(winSize);
                    sessionContext.setPtySizeSum(shellData.getChannelPtySizeSum());
                }
                try {
                    OutputStream outputStream = sessionContext.getProcess().getOutputStream();
                    outputStream.write(shellData.getCommand().getBytes());
                    outputStream.flush();
                } catch (IOException e) {
                    log.error("the web shell fails to forward messages to the terminal:{}", e.getMessage());
                }
            } else if (Constants.OPERATE_CONNECT.equals(shellData.getOperate())) {
                initPty(session, sessionContext);
            } else {
                this.close(session, "unsupported instruction");
            }
        } catch (Exception e) {
            log.error(String.format("handling exception：%s", e.getMessage()), e instanceof IllegalArgumentException ? null : e);
            this.close(session, String.format("handling exception：%s", e.getMessage()));
        }
    }


    @OnClose
    public void onClose(Session session, CloseReason closeReason) {
        log.debug("Session {} onClose because of {}", session.getId(), closeReason);
        try {
            this.close(session, closeReason.getReasonPhrase());
        } catch (Exception e) {
            log.error("close error:{}", e.getMessage());
        }
    }

    @OnError
    public void onError(Session session, Throwable t) {
        log.error("Session {} onError {}", session.getId(), t.getMessage());
        try {
            this.close(session, String.format("abnormal occurrence=%s", t.getMessage()));
        } catch (Exception e) {
            log.error("close error:{}", e.getMessage());
        }
    }

    private void initPty(Session session, SessionContext sessionContext) throws Exception {
        sendMessage(session, "connecting...");

        String execCommand = getCommand(sessionContext);
        log.debug("execCommand={}", execCommand);
        String[] cmd = execCommand.split("\\s+");
        Map<String, String> env = new HashMap<>(System.getenv());
        env.put("TERM", "xterm");

        int trySize = 3;
//        PtyProcessBuilder ptyProcessBuilder = new PtyProcessBuilder().setDirectory(k8sKubectlService.getK8sRuntimeRoot().getAbsolutePath()).setCommand(cmd).setEnvironment(env);

        PtyProcessBuilder ptyProcessBuilder = new PtyProcessBuilder().setCommand(cmd).setEnvironment(env);
        PtyProcess process = ptyProcessBuilder.start();
        log.debug("pid={}; execCommand={}", process.getPid(), execCommand);
        while (!process.isAlive() && trySize-- > 0) {
            sendMessage(session, ".");
            process = ptyProcessBuilder.start();
            log.debug("pid={}; execCommand={}", process.getPid(), execCommand);
        }

        if (process.isAlive()) {
            sessionContext.setProcess(process);
            OutputStream outputStream = sessionContext.getProcess().getOutputStream();
            outputStream.write("sh -c \"clear; (bash || ash || sh)\"\n".getBytes());
            outputStream.flush();
            //启动线程异步处理
            ThreadPoolUtils.execute(() -> {
                try (InputStream inputStream = sessionContext.getProcess().getInputStream()) {
                    byte[] buffer = new byte[Constants.BUFFER_SIZE];
                    int i;
                    //如果没有数据来，线程会一直阻塞在这个地方等待数据。
                    while ((i = inputStream.read(buffer)) != -1) {
                        sendMessage(session, new String(Arrays.copyOfRange(buffer, 0, i), StandardCharsets.UTF_8));
                    }
                } catch (IOException e) {
                    log.error("the information flow returned by the read terminal is abnormal.", e);
                }
            });
        } else {
            throw new Exception("connect failed.");
        }
    }

    private String getCommand(SessionContext sessionContext) throws Exception {

        AgentDto agentDto = v3AgentService.getAgent(sessionContext.getIp());

        //如果传入了 nodeName，优先，进入主机的shell
        if (StringUtils.hasText(sessionContext.getNodeName())) {
            //根据模板启动 node-shell-pod 再通过 对 Pod 中的容器执行命令
            String nodeShellPodName = String.format("node-shell-%s-%s", System.currentTimeMillis(), RandomStringUtils.randomAlphanumeric(5).toLowerCase());
            Map<String, Object> params = new HashMap<>();
            params.put("node_name", sessionContext.getNodeName());
            params.put("node_shell_name", nodeShellPodName);
            params.put("namespace", k8sConfigProperties.getConsole().getDefaultNamespace());
            params.put("registry.url", agentDto.getRegistryUrl());
            params.put("registry.context-path", agentDto.getRegistryContextPath().trim());

            sessionContext.setPodName(nodeShellPodName);
            sessionContext.setPodNew(true);

            k8sKubectlService.apply(sessionContext.getIp(), agentDto.getKubeConfig(), "node-shell.yaml", params);
            //由于创建 pod 需要时间,等待2秒
            Thread.sleep(k8sConfigProperties.getConsole().getNodeShellWaitTime());
        }

        return k8sKubectlService.getPodItCommand(sessionContext.getIp(), agentDto.getKubeConfig(), sessionContext.getPodName(), sessionContext.getNamespace(), sessionContext.getContainer());
    }

    private void sendMessage(Session session, String message) throws IOException {
        if (session.isOpen()) {
            session.getBasicRemote().sendText(message);
        }
    }

    private void close(Session session, String msg) {
        SessionContext sessionContext = this.sessionIdMap.remove(session.getId());
        if (sessionContext != null) {
            //断开连接
            try {
                sessionContext.close();
                sendMessage(session, msg);
                session.close();
            } catch (Exception e) {
                log.error("close session error={}", e.getMessage());
            }
            //如果是新创建的pod，需要销毁
            if (sessionContext.isPodNew()) {
                AgentDto agentDto = v3AgentService.getAgent(sessionContext.getIp());
                k8sKubectlService.delPod(sessionContext.getIp(), agentDto.getKubeConfig(), sessionContext.getPodName(), sessionContext.getNamespace());
            }
        }
    }

    @Getter
    @Setter
    static class SessionContext implements AutoCloseable {

        public SessionContext(String ip, List<NameValuePair> pairs) {
            this.ip = ip;
            Map<String, String> map = new HashMap<>();
            pairs.forEach(x -> map.put(x.getName(), x.getValue()));
            this.nodeName = map.get("node");
            this.namespace = map.get("namespace");
            this.podName = map.get("pod");
            this.container = map.get("container");
        }

        private String ip;

        private String nodeName;
        private String namespace;
        private String podName;

        //是否是新创建的pod
        private boolean podNew = false;
        private String container;

        private PtyProcess process;

        private int ptySizeSum;

        private int commandSize = 0;

        public boolean isProcessExits() {
            if (commandSize == 0 || commandSize++ > 30) {
                commandSize = 1;
                return process != null ? !process.isAlive() : true;
            } else {
                return false;
            }
        }

        @Override
        public void close() throws Exception {
            if (process != null) {
                process.destroy();
            }
        }
    }
}

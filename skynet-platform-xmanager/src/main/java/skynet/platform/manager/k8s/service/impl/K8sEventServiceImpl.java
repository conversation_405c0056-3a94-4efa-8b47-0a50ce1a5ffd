package skynet.platform.manager.k8s.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONObject;

import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.models.CoreV1Event;
import io.kubernetes.client.openapi.models.CoreV1EventList;
import io.kubernetes.client.openapi.models.V1Status;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.manager.admin.v3.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sEventService;

@Slf4j
@Service
public class K8sEventServiceImpl extends K8sBaseService implements K8sEventService {

    public K8sEventServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 Event 列表
     */
    @Override
    public List<JSONObject> getEvents(String ip, K8sQuery k8sQuery) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));
        
        List<JSONObject> eventDtoList = new ArrayList<>();
        CoreV1EventList eventList;
        if (StringUtils.isNotBlank(k8sQuery.getNamespace())) {
            eventList = api.listNamespacedEvent(k8sQuery.getNamespace()).execute();
        } else {
            eventList = api.listEventForAllNamespaces().execute();
        }
        for (CoreV1Event event : eventList.getItems()) {
            eventDtoList.add(toJSON(event));
        }
        return eventDtoList;
    }

    /**
     * 获取 Event 详情
     */
    @Override
    public JSONObject getEvent(String ip, String namespace, String eventName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));
        
        CoreV1Event event = api.readNamespacedEvent(eventName, namespace).execute();
        return toJSON(event);
    }

    /**
     * 获取 Event Yaml
     */
    @Override
    public String getEventYaml(String ip, String namespace, String eventName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));
        
        CoreV1Event event = api.readNamespacedEvent(eventName, namespace).execute();
        return Yaml.dump(event);
    }

    /**
     * 删除 Event
     */
    @Override
    public JSONObject deleteEvent(String ip, String namespace, String eventName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));
        
        V1Status status = api.deleteNamespacedEvent(eventName, namespace).execute();
        return toJSON(status);
    }
    
}

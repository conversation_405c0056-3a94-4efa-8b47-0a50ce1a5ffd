package skynet.platform.manager.k8s.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import skynet.boot.SkynetProperties;

@Getter
@Setter
@Configuration(proxyBeanMethods = false)
@ConfigurationProperties("skynet.k8s")
public class K8sConfigProperties {

    private final SkynetProperties skynetProperties;

    public K8sConfigProperties(SkynetProperties skynetProperties) {
        this.skynetProperties = skynetProperties;
    }

    /**
     * /Users/<USER>/project/skynet/skynet-platform/skynet-platform-build/misc/runtime/k8s
     * 如果没有配置，默认：{skynet_home}/runtime/k8s
     */
    private String runtimeRoot;

    /**
     * 平台信息（ 格式为：os + arch，比如 linux-arm64 或 linux-amd64 ）
     */
    private String platform = "linux-amd64";

    /**
     * agent IP 使用集群内部 IP，而不是 master IP
     */
    private boolean useInternalIp = false;

    private ConsoleConfig console = new ConsoleConfig();

    public String getRuntimeRoot() {
        return StringUtils.hasText(runtimeRoot) ? runtimeRoot : String.format("%s/runtime/k8s", skynetProperties.getHome());
    }

    @Getter
    @Setter
    public static class ConsoleConfig {

        /**
         * 是否开启，默认true
         */
        private boolean enabled = true;

        private String defaultNamespace = "skynet-system";

        private int nodeShellWaitTime = 2000;
    }
}

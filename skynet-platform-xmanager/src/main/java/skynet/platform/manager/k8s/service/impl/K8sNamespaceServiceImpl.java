package skynet.platform.manager.k8s.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONObject;

import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.models.V1Namespace;
import io.kubernetes.client.openapi.models.V1NamespaceList;
import io.kubernetes.client.openapi.models.V1Status;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.manager.admin.v3.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sNamespaceService;

@Slf4j
@Service
public class K8sNamespaceServiceImpl extends K8sBaseService implements K8sNamespaceService {

    public K8sNamespaceServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 Namespace 列表
     */
    @Override
    public List<JSONObject> getNamespaces(String ip, K8sQuery k8sQuery) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));
        
        List<JSONObject> namespaceDtos = new ArrayList<>();
        V1NamespaceList namespaceList = api.listNamespace();
        for (V1Namespace namespace : namespaceList.getItems()) {
            namespaceDtos.add(toJSON(namespace));
        }
        return namespaceDtos;
    }

    /**
     * 获取 Namespace 详情
     */
    @Override
    public JSONObject getNamespace(String ip, String namespaceName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));
        
        V1Namespace namespace = api.readNamespace(namespaceName);
        return toJSON(namespace);
    }

    /**
     * 获取 Namespace Yaml
     */
    @Override
    public String getNamespaceYaml(String ip, String namespaceName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));
        
        V1Namespace namespace = api.readNamespace(namespaceName);
        return Yaml.dump(namespace);
    }

    /**
     * 删除 Namespace
     */
    @Override
    public JSONObject deleteNamespace(String ip, String namespaceName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));
        
        V1Status status = api.deleteNamespace(namespaceName);
        return toJSON(status);
    }
    
}

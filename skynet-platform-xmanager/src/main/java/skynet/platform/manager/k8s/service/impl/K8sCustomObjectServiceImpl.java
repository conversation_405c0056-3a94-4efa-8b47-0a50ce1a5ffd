package skynet.platform.manager.k8s.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import io.kubernetes.client.openapi.apis.CustomObjectsApi;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.manager.admin.v3.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sCustomObjectService;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class K8sCustomObjectServiceImpl extends K8sBaseService implements K8sCustomObjectService {

    public K8sCustomObjectServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 CustomObject 列表
     */
    @Override
    public List<JSONObject> getCustomObjects(String ip, String group, String version, String plural, K8sQuery k8sQuery) throws Exception {

        CustomObjectsApi api = new CustomObjectsApi(initApiClient(ip));

        List<JSONObject> customObjectsDtoList = new ArrayList<>();
        Object customObjectsList;
        if (StringUtils.isNotBlank(k8sQuery.getNamespace())) {
            customObjectsList = api.listNamespacedCustomObject(group, version, k8sQuery.getNamespace(), plural).execute();
        } else {
            customObjectsList = api.listClusterCustomObject(group, version, plural).execute();
        }
        JSONArray items = toJSON(customObjectsList).getJSONArray("items");
        for (int i = 0; i < items.size(); i++) {
            customObjectsDtoList.add(toJSON(items.get(i)));
        }
        return customObjectsDtoList;
    }

    /**
     * 获取 CustomObject 详情（namespace scope）
     */
    @Override
    public JSONObject getCustomObject(String ip, String group, String version, String plural, String namespace, String customObjectName) throws Exception {

        CustomObjectsApi api = new CustomObjectsApi(initApiClient(ip));

        Object customObject = api.getNamespacedCustomObject(group, version, namespace, plural, customObjectName).execute();
        return toJSON(customObject);
    }

    /**
     * 获取 CustomObject 详情（cluster scope）
     */
    @Override
    public JSONObject getCustomObject(String ip, String group, String version, String plural, String customObjectName) throws Exception {

        CustomObjectsApi api = new CustomObjectsApi(initApiClient(ip));

        Object customObject = api.getClusterCustomObject(group, version, plural, customObjectName).execute();
        return toJSON(customObject);
    }

    /**
     * 获取 CustomObject Yaml（namespace scope）
     */
    @Override
    public String getCustomObjectYaml(String ip, String group, String version, String plural, String namespace, String customObjectName) throws Exception {

        CustomObjectsApi api = new CustomObjectsApi(initApiClient(ip));

        Object customObject = api.getNamespacedCustomObject(group, version, namespace, plural, customObjectName).execute();
        return Yaml.dump(customObject);
    }

    /**
     * 获取 CustomObject Yaml（cluster scope）
     */
    @Override
    public String getCustomObjectYaml(String ip, String group, String version, String plural, String customObjectName) throws Exception {

        CustomObjectsApi api = new CustomObjectsApi(initApiClient(ip));

        Object customObject = api.getClusterCustomObject(group, version, plural, customObjectName).execute();
        return Yaml.dump(customObject);
    }
}

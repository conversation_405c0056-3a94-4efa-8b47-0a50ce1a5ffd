package skynet.platform.manager.k8s.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import io.kubernetes.client.custom.V1Patch;
import io.kubernetes.client.openapi.apis.AppsV1Api;
import io.kubernetes.client.openapi.models.V1Deployment;
import io.kubernetes.client.openapi.models.V1DeploymentList;
import io.kubernetes.client.openapi.models.V1ReplicaSet;
import io.kubernetes.client.openapi.models.V1Status;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.K8sRollbackDeployment;
import skynet.platform.feign.model.K8sUpdateDeploymentStrategy;
import skynet.platform.feign.model.K8sUpdateImage;
import skynet.platform.feign.model.K8sUpdateReplica;
import skynet.platform.feign.model.K8sUpdateRevisionHistoryLimit;
import skynet.platform.manager.admin.v3.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sDeploymentService;

/**
 * Kubernetes Deployment 服务实现类
 *
 * 升级说明：
 * - 兼容 kubernetes-client-java 24.0.0 版本
 * - 移除了已弃用的 PatchUtils.patch 方法，改为直接调用 API
 * - 更新了所有 patch 操作以使用新的 API 模式
 * - 保持原有功能的完整性和兼容性
 */
@Slf4j
@Service
public class K8sDeploymentServiceImpl extends K8sWorkloadService implements K8sDeploymentService {

    public K8sDeploymentServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 Deployment 列表
     */
    @Override
    public List<JSONObject> getDeployments(String ip, K8sQuery k8sQuery) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        List<JSONObject> deploymentDtos = new ArrayList<>();
        V1DeploymentList deploymentList;
        if (StringUtils.isNotBlank(k8sQuery.getNamespace())) {
            deploymentList = api.listNamespacedDeployment(k8sQuery.getNamespace());
        } else {
            deploymentList = api.listDeploymentForAllNamespaces();
        }
        for (V1Deployment deployment : deploymentList.getItems()) {
            deploymentDtos.add(toJSON(deployment));
        }
        return deploymentDtos;
    }

    /**
     * 获取 Deployment 详情
     */
    @Override
    public JSONObject getDeployment(String ip, String namespace, String deploymentName) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));
        
        V1Deployment deployment = api.readNamespacedDeployment(deploymentName, namespace);
        return toJSON(deployment);
    }

    /**
     * 获取 Deployment Yaml
     */
    @Override
    public String getDeploymentYaml(String ip, String namespace, String deploymentName) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));
        
        V1Deployment deployment = api.readNamespacedDeployment(deploymentName, namespace);
        return Yaml.dump(deployment);
    }

    /**
     * 删除 Deployment
     */
    @Override
    public JSONObject deleteDeployment(String ip, String namespace, String deploymentName) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));
        
        V1Status status = api.deleteNamespacedDeployment(deploymentName, namespace);
        return toJSON(status);
    }

    /**
     * 重启 Deployment
     *
     * 升级说明：
     * - 移除已弃用的 PatchUtils.patch 方法
     * - 直接使用 AppsV1Api.patchNamespacedDeployment 方法
     * - 在 24.0.0 版本中推荐直接调用 API 方法
     *
     * @param ip 集群节点IP地址
     * @param namespace 命名空间
     * @param deploymentName Deployment 名称
     * @return 重启后的 Deployment 对象
     * @throws Exception 当重启失败时抛出
     */
    @Override
    public JSONObject restartDeployment(String ip, String namespace, String deploymentName) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        // strategic-merge-patch a deployment
        V1Patch patch = buildRestartPatch();
        V1Deployment deployment = api.patchNamespacedDeployment(
            deploymentName,
            namespace,
            patch,
            null,
            null,
            null,
            null
        );
        return toJSON(deployment);
    }

    /**
     * 伸缩
     *
     * 升级说明：
     * - 移除已弃用的 PatchUtils.patch 方法
     * - 直接使用 AppsV1Api.patchNamespacedDeployment 方法
     * - 在 24.0.0 版本中推荐直接调用 API 方法
     *
     * @param ip 集群节点IP地址
     * @param namespace 命名空间
     * @param deploymentName Deployment 名称
     * @param k8sUpdateReplica 副本数更新信息
     * @return 更新后的 Deployment 对象
     * @throws Exception 当更新失败时抛出
     */
    @Override
    public JSONObject updateDeploymentReplicas(String ip, String namespace, String deploymentName, K8sUpdateReplica k8sUpdateReplica) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        // json-patch a deployment
        V1Patch patch = buildUpdateReplicaPatch(k8sUpdateReplica);
        V1Deployment deployment = api.patchNamespacedDeployment(
            deploymentName,
            namespace,
            patch,
            null,
            null,
            null,
            null
        );
        return toJSON(deployment);
    }

    /**
     * 调整镜像版本
     *
     * 升级说明：
     * - 移除已弃用的 PatchUtils.patch 方法
     * - 直接使用 AppsV1Api.patchNamespacedDeployment 方法
     * - 在 24.0.0 版本中推荐直接调用 API 方法
     *
     * @param ip 集群节点IP地址
     * @param namespace 命名空间
     * @param deploymentName Deployment 名称
     * @param k8sUpdateImage 镜像更新信息
     * @return 更新后的 Deployment 对象
     * @throws Exception 当更新失败时抛出
     */
    @Override
    public JSONObject updateDeploymentImages(String ip, String namespace, String deploymentName, K8sUpdateImage k8sUpdateImage) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        // json-patch a deployment
        V1Patch patch = buildUpdateImagePatch(k8sUpdateImage);
        V1Deployment deployment = api.patchNamespacedDeployment(
            deploymentName,
            namespace,
            patch,
            null,
            null,
            null,
            null
        );
        return toJSON(deployment);
    }
    
    /**
     * 调整最大历史副本数
     *
     * 升级说明：
     * - 移除已弃用的 PatchUtils.patch 方法
     * - 直接使用 AppsV1Api.patchNamespacedDeployment 方法
     * - 在 24.0.0 版本中推荐直接调用 API 方法
     *
     * @param ip 集群节点IP地址
     * @param namespace 命名空间
     * @param deploymentName Deployment 名称
     * @param k8sUpdateRevisionHistoryLimit 历史版本限制更新信息
     * @return 更新后的 Deployment 对象
     * @throws Exception 当更新失败时抛出
     */
    @Override
    public JSONObject updateDeploymentRevisionHistoryLimit(String ip, String namespace, String deploymentName, K8sUpdateRevisionHistoryLimit k8sUpdateRevisionHistoryLimit) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        // json-patch a deployment
        V1Patch patch = buildUpdateRevisionHistoryLimitPatch(k8sUpdateRevisionHistoryLimit);
        V1Deployment deployment = api.patchNamespacedDeployment(
            deploymentName,
            namespace,
            patch,
            null,
            null,
            null,
            null
        );
        return toJSON(deployment);
    }

    /**
     * 回滚版本
     *
     * 升级说明：
     * - 移除已弃用的 PatchUtils.patch 方法
     * - 直接使用 AppsV1Api.patchNamespacedDeployment 方法
     * - 在 24.0.0 版本中推荐直接调用 API 方法
     *
     * @param ip 集群节点IP地址
     * @param namespace 命名空间
     * @param deploymentName Deployment 名称
     * @param k8sRollbackDeployment 回滚信息
     * @return 回滚后的 Deployment 对象
     * @throws Exception 当回滚失败时抛出
     */
    @Override
    public JSONObject rollbackDeployment(String ip, String namespace, String deploymentName, K8sRollbackDeployment k8sRollbackDeployment) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        V1ReplicaSet replicaSet = api.readNamespacedReplicaSet(k8sRollbackDeployment.getReplicaSetName(), namespace);
        if (replicaSet == null) {
            throw new ApiRequestException(ApiRequestErrorCode.PARAM_ILLEGAL);
        }

        // json-patch a deployment
        JSONArray patch = new JSONArray();
        patch.add(new JSONObject()
            .fluentPut("op", "replace")
            .fluentPut("path", "/spec/template")
            .fluentPut("value", replicaSet.getSpec().getTemplate()));

        V1Patch patchBody = new V1Patch(toJSONString(patch));
        V1Deployment deployment = api.patchNamespacedDeployment(
            deploymentName,
            namespace,
            patchBody,
            null,
            null,
            null,
            null
        );
        return toJSON(deployment);
    }

    /**
     * 修改更新策略
     *
     * 升级说明：
     * - 移除已弃用的 PatchUtils.patch 方法
     * - 直接使用 AppsV1Api.patchNamespacedDeployment 方法
     * - 在 24.0.0 版本中推荐直接调用 API 方法
     * - 保持对 RollingUpdate 策略中百分比和整数值的支持
     *
     * @param ip 集群节点IP地址
     * @param namespace 命名空间
     * @param deploymentName Deployment 名称
     * @param k8sUpdateDeploymentStrategy 更新策略信息
     * @return 更新后的 Deployment 对象
     * @throws Exception 当更新失败时抛出
     */
    @Override
    public JSONObject updateDeploymentStrategy(String ip, String namespace, String deploymentName, K8sUpdateDeploymentStrategy k8sUpdateDeploymentStrategy) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));

        // 注意 rollingUpdate 的值可能是字符串型（20%），也可能是整型（3）
        JSONObject strategy = new JSONObject();
        strategy.put("type", k8sUpdateDeploymentStrategy.getType());
        if ("RollingUpdate".equals(k8sUpdateDeploymentStrategy.getType())) {
            JSONObject rollingUpdate = new JSONObject();
            rollingUpdate.put("maxSurge", getIntOrStr(k8sUpdateDeploymentStrategy.getRollingUpdate().getMaxSurge()));
            rollingUpdate.put("maxUnavailable", getIntOrStr(k8sUpdateDeploymentStrategy.getRollingUpdate().getMaxUnavailable()));
            strategy.put("rollingUpdate", rollingUpdate);
        }

        // json-patch a deployment
        V1Patch patch = buildUpdateStrategyPatch(strategy);
        V1Deployment deployment = api.patchNamespacedDeployment(
            deploymentName,
            namespace,
            patch,
            null,
            null,
            null,
            null
        );
        return toJSON(deployment);
    }
}

package skynet.platform.manager.k8s.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONObject;

import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.models.V1Secret;
import io.kubernetes.client.openapi.models.V1SecretList;
import io.kubernetes.client.openapi.models.V1Status;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.manager.admin.v3.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sSecretService;

@Slf4j
@Service
public class K8sSecretServiceImpl extends K8sBaseService implements K8sSecretService {

    public K8sSecretServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 Secret 列表
     */
    @Override
    public List<JSONObject> getSecrets(String ip, K8sQuery k8sQuery) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));
        
        List<JSONObject> secretDtoList = new ArrayList<>();
        V1SecretList secretList;
        if (StringUtils.isNotBlank(k8sQuery.getNamespace())) {
            secretList = api.listNamespacedSecret(k8sQuery.getNamespace()).execute();
        } else {
            secretList = api.listSecretForAllNamespaces().execute();
        }
        for (V1Secret secret : secretList.getItems()) {
            secretDtoList.add(toJSON(secret));
        }
        return secretDtoList;
    }

    /**
     * 获取 Secret 详情
     */
    @Override
    public JSONObject getSecret(String ip, String namespace, String secretName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));
        
        V1Secret secret = api.readNamespacedSecret(secretName, namespace).execute();
        return toJSON(secret);
    }

    /**
     * 获取 Secret Yaml
     */
    @Override
    public String getSecretYaml(String ip, String namespace, String secretName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));
        
        V1Secret secret = api.readNamespacedSecret(secretName, namespace).execute();
        return Yaml.dump(secret);
    }

    /**
     * 删除 Secret
     */
    @Override
    public JSONObject deleteSecret(String ip, String namespace, String secretName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));
        
        V1Status status = api.deleteNamespacedSecret(secretName, namespace).execute();
        return toJSON(status);
    }
    
}

package skynet.platform.manager.k8s.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONObject;

import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.models.V1ConfigMap;
import io.kubernetes.client.openapi.models.V1ConfigMapList;
import io.kubernetes.client.openapi.models.V1Status;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.manager.admin.v3.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sConfigMapService;

@Slf4j
@Service
public class K8sConfigMapServiceImpl extends K8sBaseService implements K8sConfigMapService {

    public K8sConfigMapServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 ConfigMap 列表
     */
    @Override
    public List<JSONObject> getConfigMaps(String ip, K8sQuery k8sQuery) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        List<JSONObject> configMapDtoList = new ArrayList<>();
        V1ConfigMapList configMapList;
        if (StringUtils.isNotBlank(k8sQuery.getNamespace())) {
            configMapList = api.listNamespacedConfigMap(k8sQuery.getNamespace()).execute();
        } else {
            configMapList = api.listConfigMapForAllNamespaces().execute();
        }
        // 应用 K8sQuery 过滤条件
        List<V1ConfigMap> filteredConfigMaps = applyK8sQueryFilter(configMapList.getItems(), k8sQuery);
        
        for (V1ConfigMap configMap : filteredConfigMaps) {
            configMapDtoList.add(toJSON(configMap));
        }
        return configMapDtoList;
    }

    /**
     * 获取 ConfigMap 详情
     */
    @Override
    public JSONObject getConfigMap(String ip, String namespace, String configMapName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));
        
        V1ConfigMap configMap = api.readNamespacedConfigMap(configMapName, namespace).execute();
        return toJSON(configMap);
    }

    /**
     * 获取 ConfigMap Yaml
     */
    @Override
    public String getConfigMapYaml(String ip, String namespace, String configMapName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));
        
        V1ConfigMap configMap = api.readNamespacedConfigMap(configMapName, namespace).execute();
        return Yaml.dump(configMap);
    }

    /**
     * 删除 ConfigMap
     */
    @Override
    public JSONObject deleteConfigMap(String ip, String namespace, String configMapName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));
        
        V1Status status = api.deleteNamespacedConfigMap(configMapName, namespace).execute();
        return toJSON(status);
    }
    
}

package skynet.platform.manager.k8s.service.impl;

import com.alibaba.fastjson2.JSONObject;
import io.kubernetes.client.openapi.apis.ApiextensionsV1Api;
import io.kubernetes.client.openapi.models.V1CustomResourceDefinition;
import io.kubernetes.client.openapi.models.V1CustomResourceDefinitionList;
import io.kubernetes.client.openapi.models.V1Status;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.manager.admin.v3.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sCustomResourceDefinitionService;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class K8sCustomResourceDefinitionServiceImpl extends K8sBaseService implements K8sCustomResourceDefinitionService {

    public K8sCustomResourceDefinitionServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 CustomResourceDefinition 列表
     */
    @Override
    public List<JSONObject> getCustomResourceDefinitions(String ip, K8sQuery k8sQuery) throws Exception {

        ApiextensionsV1Api api = new ApiextensionsV1Api(initApiClient(ip));

        List<JSONObject> crdDtoList = new ArrayList<>();
        V1CustomResourceDefinitionList crdList = api.listCustomResourceDefinition().execute();
        // 应用 K8sQuery 过滤条件 (CRD 是集群级别资源，只支持 fieldSelector 和 labelSelector)
        List<V1CustomResourceDefinition> filteredCRDs = applyK8sQueryFilter(crdList.getItems(), k8sQuery);

        for (V1CustomResourceDefinition crd : filteredCRDs) {
            crdDtoList.add(toJSON(crd));
        }
        return crdDtoList;
    }

    /**
     * 获取 CustomResourceDefinition 详情
     */
    @Override
    public JSONObject getCustomResourceDefinition(String ip, String crdName) throws Exception {

        ApiextensionsV1Api api = new ApiextensionsV1Api(initApiClient(ip));

        V1CustomResourceDefinition crd = api.readCustomResourceDefinition(crdName).execute();
        return toJSON(crd);
    }

    /**
     * 获取 CustomResourceDefinition Yaml
     */
    @Override
    public String getCustomResourceDefinitionYaml(String ip, String crdName) throws Exception {

        ApiextensionsV1Api api = new ApiextensionsV1Api(initApiClient(ip));

        V1CustomResourceDefinition crd = api.readCustomResourceDefinition(crdName).execute();
        return Yaml.dump(crd);
    }

    /**
     * 删除 CustomResourceDefinition
     */
    @Override
    public JSONObject deleteCustomResourceDefinition(String ip, String crdName) throws Exception {

        ApiextensionsV1Api api = new ApiextensionsV1Api(initApiClient(ip));

        V1Status status = api.deleteCustomResourceDefinition(crdName).execute();
        return toJSON(status);
    }

}

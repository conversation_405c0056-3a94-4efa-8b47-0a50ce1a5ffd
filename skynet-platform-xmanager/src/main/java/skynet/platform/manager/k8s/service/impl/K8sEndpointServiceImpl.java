package skynet.platform.manager.k8s.service.impl;

import com.alibaba.fastjson2.JSONObject;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.models.V1Endpoints;
import io.kubernetes.client.openapi.models.V1EndpointsList;
import io.kubernetes.client.openapi.models.V1Status;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.manager.admin.v3.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sEndpointService;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class K8sEndpointServiceImpl extends K8sBaseService implements K8sEndpointService {

    public K8sEndpointServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 Endpoint 列表
     */
    @Override
    public List<JSONObject> getEndpoints(String ip, K8sQuery k8sQuery) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        List<JSONObject> endpointDtoList = new ArrayList<>();
        V1EndpointsList endpointList;
        if (StringUtils.isNotBlank(k8sQuery.getNamespace())) {
            endpointList = api.listNamespacedEndpoints(k8sQuery.getNamespace()).execute();
        } else {
            endpointList = api.listEndpointsForAllNamespaces().execute();
        }
        // 应用 K8sQuery 过滤条件
        List<V1Endpoints> filteredEndpoints = applyK8sQueryFilter(endpointList.getItems(), k8sQuery);

        for (V1Endpoints endpoint : filteredEndpoints) {
            endpointDtoList.add(toJSON(endpoint));
        }
        return endpointDtoList;
    }

    /**
     * 获取 Endpoint 详情
     */
    @Override
    public JSONObject getEndpoint(String ip, String namespace, String endpointName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        V1Endpoints endpoint = api.readNamespacedEndpoints(endpointName, namespace).execute();
        return toJSON(endpoint);
    }

    /**
     * 获取 Endpoint Yaml
     */
    @Override
    public String getEndpointYaml(String ip, String namespace, String endpointName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        V1Endpoints endpoint = api.readNamespacedEndpoints(endpointName, namespace).execute();
        return Yaml.dump(endpoint);
    }

    /**
     * 删除 Endpoint
     */
    @Override
    public JSONObject deleteEndpoint(String ip, String namespace, String endpointName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        V1Status status = api.deleteNamespacedEndpoints(endpointName, namespace).execute();
        return toJSON(status);
    }

}

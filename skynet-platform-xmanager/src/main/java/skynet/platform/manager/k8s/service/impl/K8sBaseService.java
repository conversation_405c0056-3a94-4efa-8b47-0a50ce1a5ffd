package skynet.platform.manager.k8s.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.gson.*;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.util.ClientBuilder;
import io.kubernetes.client.util.KubeConfig;
import org.apache.commons.lang3.StringUtils;
import skynet.platform.feign.model.AgentDto;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.manager.admin.v3.service.V3AgentService;

import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Kubernetes 基础服务类
 * <p>
 * 升级说明：
 * - 兼容 kubernetes-client-java 24.0.0 版本
 * - 保持原有的客户端初始化和对象转换功能
 * - 添加了详细的注释说明
 */
public class K8sBaseService {

    private final V3AgentService v3AgentService;

    public K8sBaseService(V3AgentService v3AgentService) {
        this.v3AgentService = v3AgentService;
    }

    /**
     * 初始化 Kubernetes 客户端
     * <p>
     * 升级说明：
     * - ClientBuilder.kubeconfig() 方法在 24.0.0 版本中保持兼容
     * - KubeConfig.loadKubeConfig() 方法签名未变更
     *
     * @param ip 集群节点IP地址
     * @return 初始化完成的 ApiClient 实例
     * @throws IOException 当配置文件读取失败时抛出
     */
    public ApiClient initApiClient(String ip) throws IOException {
        // TODO: 缓存优化 - 考虑添加客户端缓存以提高性能
        AgentDto agentDto = v3AgentService.getAgent(ip);
        return ClientBuilder.kubeconfig(
                KubeConfig.loadKubeConfig(new StringReader(agentDto.getKubeConfig()))
        ).build();
    }

    /**
     * 将 K8S 资源对象转换为 JSONObject
     * <p>
     * 升级说明：
     * - 修复了 Java 9+ 模块系统中 Gson 序列化 OffsetDateTime 的问题
     * - 使用配置了时间类型适配器的 Gson，避免反射访问限制
     * - 移除 managedFields 字段以减少响应体积
     * - 在 24.0.0 版本中推荐使用自定义 Gson 配置
     *
     * @param object Kubernetes 资源对象
     * @return 转换后的 JSONObject，已移除冗余字段
     */
    public JSONObject toJSON(Object object) {
        if (object == null) {
            return new JSONObject();
        }

        try {
            // 使用配置了时间类型适配器的 Gson，避免 OffsetDateTime 反射问题
            Gson gson = createGsonWithTimeAdapters();
            JSONObject json = JSON.parseObject(gson.toJson(object));

            // 去掉一些冗余字段，减少响应体积
            if (json.containsKey("metadata") && json.getJSONObject("metadata") != null) {
                json.getJSONObject("metadata").remove("managedFields");
            }
            return json;
        } catch (Exception e) {
            // 如果 Gson 序列化失败，使用 FastJSON 直接序列化作为备选方案
            try {
                JSONObject json = (JSONObject) JSON.toJSON(object);
                // 去掉一些冗余字段，减少响应体积
                if (json.containsKey("metadata") && json.getJSONObject("metadata") != null) {
                    json.getJSONObject("metadata").remove("managedFields");
                }
                return json;
            } catch (Exception ex) {
                // 最后的备选方案：返回空对象
                return new JSONObject();
            }
        }
    }

    /**
     * 将 K8S 资源对象转换为 JSONString
     * <p>
     * 升级说明：
     * - 修复了 Java 9+ 模块系统中 Gson 序列化 OffsetDateTime 的问题
     * - 使用配置了时间类型适配器的 Gson，避免反射访问限制
     * - 在 24.0.0 版本中推荐使用自定义 Gson 配置
     *
     * @param object Kubernetes 资源对象
     * @return 序列化后的 JSON 字符串
     */
    public String toJSONString(Object object) {
        if (object == null) {
            return "{}";
        }

        try {
            // 使用配置了时间类型适配器的 Gson，避免 OffsetDateTime 反射问题
            Gson gson = createGsonWithTimeAdapters();
            return gson.toJson(object);
        } catch (Exception e) {
            // 如果 Gson 序列化失败，使用 FastJSON 作为备选方案
            return JSON.toJSONString(object);
        }
    }

    /**
     * 创建配置了时间类型适配器的 Gson 实例
     * <p>
     * 升级说明：
     * - 新增方法，用于解决 Java 9+ 模块系统中的反射访问限制
     * - 为 OffsetDateTime、LocalDateTime 等时间类型配置自定义适配器
     * - 避免 Gson 尝试通过反射访问 java.time 包的私有字段
     *
     * @return 配置了时间类型适配器的 Gson 实例
     */
    private Gson createGsonWithTimeAdapters() {
        return new GsonBuilder()
                .registerTypeAdapter(java.time.OffsetDateTime.class, new JsonSerializer<java.time.OffsetDateTime>() {
                    @Override
                    public JsonElement serialize(java.time.OffsetDateTime src, java.lang.reflect.Type typeOfSrc, JsonSerializationContext context) {
                        return new JsonPrimitive(src.toString());
                    }
                })
                .registerTypeAdapter(java.time.LocalDateTime.class, new JsonSerializer<java.time.LocalDateTime>() {
                    @Override
                    public JsonElement serialize(java.time.LocalDateTime src, java.lang.reflect.Type typeOfSrc, JsonSerializationContext context) {
                        return new JsonPrimitive(src.toString());
                    }
                })
                .registerTypeAdapter(java.time.LocalDate.class, new JsonSerializer<java.time.LocalDate>() {
                    @Override
                    public JsonElement serialize(java.time.LocalDate src, java.lang.reflect.Type typeOfSrc, JsonSerializationContext context) {
                        return new JsonPrimitive(src.toString());
                    }
                })
                .registerTypeAdapter(java.time.LocalTime.class, new JsonSerializer<java.time.LocalTime>() {
                    @Override
                    public JsonElement serialize(java.time.LocalTime src, java.lang.reflect.Type typeOfSrc, JsonSerializationContext context) {
                        return new JsonPrimitive(src.toString());
                    }
                })
                .registerTypeAdapter(java.time.ZonedDateTime.class, new JsonSerializer<java.time.ZonedDateTime>() {
                    @Override
                    public JsonElement serialize(java.time.ZonedDateTime src, java.lang.reflect.Type typeOfSrc, JsonSerializationContext context) {
                        return new JsonPrimitive(src.toString());
                    }
                })
                .registerTypeAdapter(java.time.Instant.class, new JsonSerializer<java.time.Instant>() {
                    @Override
                    public JsonElement serialize(java.time.Instant src, java.lang.reflect.Type typeOfSrc, JsonSerializationContext context) {
                        return new JsonPrimitive(src.toString());
                    }
                })
                .create();
    }

    /**
     * 应用 K8sQuery 过滤条件到资源列表
     * <p>
     * 升级说明：
     * - 新增方法，用于统一处理 fieldSelector 和 labelSelector 过滤
     * - 在客户端进行过滤，确保所有服务类都能正确使用过滤条件
     * - 支持多种过滤条件的组合使用
     *
     * @param items    资源列表
     * @param k8sQuery 查询条件
     * @return 过滤后的资源列表
     */
    protected <T> List<T> applyK8sQueryFilter(List<T> items, K8sQuery k8sQuery) {
        if (items == null || items.isEmpty()) {
            return items;
        }

        List<T> filteredItems = new ArrayList<>(items);

        // 应用 fieldSelector 过滤
        if (StringUtils.isNotBlank(k8sQuery.getFieldSelector())) {
            filteredItems = applyFieldSelectorFilter(filteredItems, k8sQuery.getFieldSelector());
        }

        // 应用 labelSelector 过滤
        if (StringUtils.isNotBlank(k8sQuery.getLabelSelector())) {
            filteredItems = applyLabelSelectorFilter(filteredItems, k8sQuery.getLabelSelector());
        }

        return filteredItems;
    }

    /**
     * 应用字段选择器过滤
     *
     * @param items         资源列表
     * @param fieldSelector 字段选择器，例如 "metadata.name=my-pod"
     * @return 过滤后的资源列表
     */
    private <T> List<T> applyFieldSelectorFilter(List<T> items, String fieldSelector) {
        // 解析字段选择器，支持格式：field=value 或 field!=value
        String[] selectors = fieldSelector.split(",");

        return items.stream().filter(item -> {
            JSONObject itemJson = toJSON(item);

            for (String selector : selectors) {
                selector = selector.trim();
                boolean isNotEqual = selector.contains("!=");
                String[] parts = isNotEqual ? selector.split("!=") : selector.split("=");

                if (parts.length != 2) {
                    continue; // 跳过格式不正确的选择器
                }

                String fieldPath = parts[0].trim();
                String expectedValue = parts[1].trim();

                // 获取字段值
                String actualValue = getFieldValue(itemJson, fieldPath);

                // 应用过滤条件
                if (isNotEqual) {
                    if (expectedValue.equals(actualValue)) {
                        return false; // 不匹配条件
                    }
                } else {
                    if (!expectedValue.equals(actualValue)) {
                        return false; // 不匹配条件
                    }
                }
            }
            return true; // 所有条件都匹配
        }).collect(Collectors.toList());
    }

    /**
     * 应用标签选择器过滤
     *
     * @param items         资源列表
     * @param labelSelector 标签选择器，例如 "app=nginx,version=1.0"
     * @return 过滤后的资源列表
     */
    private <T> List<T> applyLabelSelectorFilter(List<T> items, String labelSelector) {
        // 解析标签选择器，支持格式：key=value 或 key!=value
        String[] selectors = labelSelector.split(",");

        return items.stream().filter(item -> {
            JSONObject itemJson = toJSON(item);
            JSONObject labels = itemJson.getJSONObject("metadata");
            if (labels != null) {
                labels = labels.getJSONObject("labels");
            }

            for (String selector : selectors) {
                selector = selector.trim();
                boolean isNotEqual = selector.contains("!=");
                String[] parts = isNotEqual ? selector.split("!=") : selector.split("=");

                if (parts.length != 2) {
                    continue; // 跳过格式不正确的选择器
                }

                String labelKey = parts[0].trim();
                String expectedValue = parts[1].trim();

                // 获取标签值
                String actualValue = labels != null ? labels.getString(labelKey) : null;

                // 应用过滤条件
                if (isNotEqual) {
                    if (expectedValue.equals(actualValue)) {
                        return false; // 不匹配条件
                    }
                } else {
                    if (!expectedValue.equals(actualValue)) {
                        return false; // 不匹配条件
                    }
                }
            }
            return true; // 所有条件都匹配
        }).collect(Collectors.toList());
    }

    /**
     * 获取字段值，支持嵌套路径，例如 "metadata.name"
     *
     * @param json      JSON 对象
     * @param fieldPath 字段路径
     * @return 字段值
     */
    private String getFieldValue(JSONObject json, String fieldPath) {
        String[] pathParts = fieldPath.split("\\.");
        JSONObject current = json;

        for (int i = 0; i < pathParts.length - 1; i++) {
            current = current.getJSONObject(pathParts[i]);
            if (current == null) {
                return null;
            }
        }

        return current != null ? current.getString(pathParts[pathParts.length - 1]) : null;
    }
}

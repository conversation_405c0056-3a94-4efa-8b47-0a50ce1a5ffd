package skynet.platform.manager.k8s.service.impl;

import java.io.IOException;
import java.io.StringReader;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.gson.Gson;

import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.util.ClientBuilder;
import io.kubernetes.client.util.KubeConfig;
import skynet.platform.feign.model.AgentDto;
import skynet.platform.manager.admin.v3.service.V3AgentService;

/**
 * Kubernetes 基础服务类
 *
 * 升级说明：
 * - 兼容 kubernetes-client-java 24.0.0 版本
 * - 保持原有的客户端初始化和对象转换功能
 * - 添加了详细的注释说明
 */
public class K8sBaseService {

    private final V3AgentService v3AgentService;

    public K8sBaseService(V3AgentService v3AgentService) {
        this.v3AgentService = v3AgentService;
    }

    /**
     * 初始化 Kubernetes 客户端
     *
     * 升级说明：
     * - ClientBuilder.kubeconfig() 方法在 24.0.0 版本中保持兼容
     * - KubeConfig.loadKubeConfig() 方法签名未变更
     *
     * @param ip 集群节点IP地址
     * @return 初始化完成的 ApiClient 实例
     * @throws IOException 当配置文件读取失败时抛出
     */
    public ApiClient initApiClient(String ip) throws IOException {
        // TODO: 缓存优化 - 考虑添加客户端缓存以提高性能
        AgentDto agentDto = v3AgentService.getAgent(ip);
        return ClientBuilder.kubeconfig(
            KubeConfig.loadKubeConfig(new StringReader(agentDto.getKubeConfig()))
        ).build();
    }

    /**
     * 将 K8S 资源对象转换为 JSONObject
     *
     * 升级说明：
     * - Gson 序列化在 24.0.0 版本中保持兼容
     * - 移除 managedFields 字段以减少响应体积
     * - 使用 fastjson2 进行 JSON 解析，避免序列化异常
     *
     * @param object Kubernetes 资源对象
     * @return 转换后的 JSONObject，已移除冗余字段
     */
    public JSONObject toJSON(Object object) {

        // 使用 Gson 来序列化，使用 fastjson 会抛出异常
        Gson gson = new Gson();
        JSONObject json = JSON.parseObject(gson.toJson(object));

        // 去掉一些冗余字段，减少响应体积
        if (json.containsKey("metadata") && json.getJSONObject("metadata") != null) {
            json.getJSONObject("metadata").remove("managedFields");
        }
        return json;
    }

    /**
     * 将 K8S 资源对象转换为 JSONString
     *
     * 升级说明：
     * - 保持原有的 Gson 序列化方式
     * - 在 24.0.0 版本中无需修改
     *
     * @param object Kubernetes 资源对象
     * @return 序列化后的 JSON 字符串
     */
    public String toJSONString(Object object) {

        // 使用 Gson 来序列化
        Gson gson = new Gson();
        return gson.toJson(object);
    }
}

package skynet.platform.manager.k8s.service.impl;

import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.gson.Gson;

import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.util.ClientBuilder;
import io.kubernetes.client.util.KubeConfig;
import skynet.platform.feign.model.AgentDto;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.manager.admin.v3.service.V3AgentService;

/**
 * Kubernetes 基础服务类
 *
 * 升级说明：
 * - 兼容 kubernetes-client-java 24.0.0 版本
 * - 保持原有的客户端初始化和对象转换功能
 * - 添加了详细的注释说明
 */
public class K8sBaseService {

    private final V3AgentService v3AgentService;

    public K8sBaseService(V3AgentService v3AgentService) {
        this.v3AgentService = v3AgentService;
    }

    /**
     * 初始化 Kubernetes 客户端
     *
     * 升级说明：
     * - ClientBuilder.kubeconfig() 方法在 24.0.0 版本中保持兼容
     * - KubeConfig.loadKubeConfig() 方法签名未变更
     *
     * @param ip 集群节点IP地址
     * @return 初始化完成的 ApiClient 实例
     * @throws IOException 当配置文件读取失败时抛出
     */
    public ApiClient initApiClient(String ip) throws IOException {
        // TODO: 缓存优化 - 考虑添加客户端缓存以提高性能
        AgentDto agentDto = v3AgentService.getAgent(ip);
        return ClientBuilder.kubeconfig(
            KubeConfig.loadKubeConfig(new StringReader(agentDto.getKubeConfig()))
        ).build();
    }

    /**
     * 将 K8S 资源对象转换为 JSONObject
     *
     * 升级说明：
     * - Gson 序列化在 24.0.0 版本中保持兼容
     * - 移除 managedFields 字段以减少响应体积
     * - 使用 fastjson2 进行 JSON 解析，避免序列化异常
     *
     * @param object Kubernetes 资源对象
     * @return 转换后的 JSONObject，已移除冗余字段
     */
    public JSONObject toJSON(Object object) {

        // 使用 Gson 来序列化，使用 fastjson 会抛出异常
        Gson gson = new Gson();
        JSONObject json = JSON.parseObject(gson.toJson(object));

        // 去掉一些冗余字段，减少响应体积
        if (json.containsKey("metadata") && json.getJSONObject("metadata") != null) {
            json.getJSONObject("metadata").remove("managedFields");
        }
        return json;
    }

    /**
     * 将 K8S 资源对象转换为 JSONString
     *
     * 升级说明：
     * - 保持原有的 Gson 序列化方式
     * - 在 24.0.0 版本中无需修改
     *
     * @param object Kubernetes 资源对象
     * @return 序列化后的 JSON 字符串
     */
    public String toJSONString(Object object) {

        // 使用 Gson 来序列化
        Gson gson = new Gson();
        return gson.toJson(object);
    }

    /**
     * 应用 K8sQuery 过滤条件到资源列表
     *
     * 升级说明：
     * - 新增方法，用于统一处理 fieldSelector 和 labelSelector 过滤
     * - 在客户端进行过滤，确保所有服务类都能正确使用过滤条件
     * - 支持多种过滤条件的组合使用
     *
     * @param items 资源列表
     * @param k8sQuery 查询条件
     * @return 过滤后的资源列表
     */
    protected <T> List<T> applyK8sQueryFilter(List<T> items, K8sQuery k8sQuery) {
        if (items == null || items.isEmpty()) {
            return items;
        }

        List<T> filteredItems = new ArrayList<>(items);

        // 应用 fieldSelector 过滤
        if (StringUtils.isNotBlank(k8sQuery.getFieldSelector())) {
            filteredItems = applyFieldSelectorFilter(filteredItems, k8sQuery.getFieldSelector());
        }

        // 应用 labelSelector 过滤
        if (StringUtils.isNotBlank(k8sQuery.getLabelSelector())) {
            filteredItems = applyLabelSelectorFilter(filteredItems, k8sQuery.getLabelSelector());
        }

        return filteredItems;
    }

    /**
     * 应用字段选择器过滤
     *
     * @param items 资源列表
     * @param fieldSelector 字段选择器，例如 "metadata.name=my-pod"
     * @return 过滤后的资源列表
     */
    private <T> List<T> applyFieldSelectorFilter(List<T> items, String fieldSelector) {
        // 解析字段选择器，支持格式：field=value 或 field!=value
        String[] selectors = fieldSelector.split(",");

        return items.stream().filter(item -> {
            JSONObject itemJson = toJSON(item);

            for (String selector : selectors) {
                selector = selector.trim();
                boolean isNotEqual = selector.contains("!=");
                String[] parts = isNotEqual ? selector.split("!=") : selector.split("=");

                if (parts.length != 2) {
                    continue; // 跳过格式不正确的选择器
                }

                String fieldPath = parts[0].trim();
                String expectedValue = parts[1].trim();

                // 获取字段值
                String actualValue = getFieldValue(itemJson, fieldPath);

                // 应用过滤条件
                if (isNotEqual) {
                    if (expectedValue.equals(actualValue)) {
                        return false; // 不匹配条件
                    }
                } else {
                    if (!expectedValue.equals(actualValue)) {
                        return false; // 不匹配条件
                    }
                }
            }
            return true; // 所有条件都匹配
        }).collect(Collectors.toList());
    }

    /**
     * 应用标签选择器过滤
     *
     * @param items 资源列表
     * @param labelSelector 标签选择器，例如 "app=nginx,version=1.0"
     * @return 过滤后的资源列表
     */
    private <T> List<T> applyLabelSelectorFilter(List<T> items, String labelSelector) {
        // 解析标签选择器，支持格式：key=value 或 key!=value
        String[] selectors = labelSelector.split(",");

        return items.stream().filter(item -> {
            JSONObject itemJson = toJSON(item);
            JSONObject labels = itemJson.getJSONObject("metadata");
            if (labels != null) {
                labels = labels.getJSONObject("labels");
            }

            for (String selector : selectors) {
                selector = selector.trim();
                boolean isNotEqual = selector.contains("!=");
                String[] parts = isNotEqual ? selector.split("!=") : selector.split("=");

                if (parts.length != 2) {
                    continue; // 跳过格式不正确的选择器
                }

                String labelKey = parts[0].trim();
                String expectedValue = parts[1].trim();

                // 获取标签值
                String actualValue = labels != null ? labels.getString(labelKey) : null;

                // 应用过滤条件
                if (isNotEqual) {
                    if (expectedValue.equals(actualValue)) {
                        return false; // 不匹配条件
                    }
                } else {
                    if (!expectedValue.equals(actualValue)) {
                        return false; // 不匹配条件
                    }
                }
            }
            return true; // 所有条件都匹配
        }).collect(Collectors.toList());
    }

    /**
     * 获取字段值，支持嵌套路径，例如 "metadata.name"
     *
     * @param json JSON 对象
     * @param fieldPath 字段路径
     * @return 字段值
     */
    private String getFieldValue(JSONObject json, String fieldPath) {
        String[] pathParts = fieldPath.split("\\.");
        JSONObject current = json;

        for (int i = 0; i < pathParts.length - 1; i++) {
            current = current.getJSONObject(pathParts[i]);
            if (current == null) {
                return null;
            }
        }

        return current != null ? current.getString(pathParts[pathParts.length - 1]) : null;
    }
}

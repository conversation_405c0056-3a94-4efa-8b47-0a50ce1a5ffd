package skynet.platform.manager.k8s.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONObject;

import io.kubernetes.client.openapi.apis.AppsV1Api;
import io.kubernetes.client.openapi.models.V1ReplicaSet;
import io.kubernetes.client.openapi.models.V1ReplicaSetList;
import io.kubernetes.client.openapi.models.V1Status;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.manager.admin.v3.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sReplicaSetService;

@Slf4j
@Service
public class K8sReplicaSetServiceImpl extends K8sBaseService implements K8sReplicaSetService {

    public K8sReplicaSetServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 ReplicaSet 列表
     */
    @Override
    public List<JSONObject> getReplicaSets(String ip, K8sQuery k8sQuery) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));
        
        List<JSONObject> replicaSetDtoList = new ArrayList<>();
        V1ReplicaSetList replicaSetList;
        if (StringUtils.isNotBlank(k8sQuery.getNamespace())) {
            replicaSetList = api.listNamespacedReplicaSet(k8sQuery.getNamespace()).execute();
        } else {
            replicaSetList = api.listReplicaSetForAllNamespaces().execute();
        }
        for (V1ReplicaSet replicaSet : replicaSetList.getItems()) {
            replicaSetDtoList.add(toJSON(replicaSet));
        }
        return replicaSetDtoList;
    }

    /**
     * 获取 ReplicaSet 详情
     */
    @Override
    public JSONObject getReplicaSet(String ip, String namespace, String replicaSetName) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));
        
        V1ReplicaSet replicaSet = api.readNamespacedReplicaSet(replicaSetName, namespace).execute();
        return toJSON(replicaSet);
    }

    /**
     * 获取 ReplicaSet Yaml
     */
    @Override
    public String getReplicaSetYaml(String ip, String namespace, String replicaSetName) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));
        
        V1ReplicaSet replicaSet = api.readNamespacedReplicaSet(replicaSetName, namespace).execute();
        return Yaml.dump(replicaSet);
    }

    /**
     * 删除 ReplicaSet
     */
    @Override
    public JSONObject deleteReplicaSet(String ip, String namespace, String replicaSetName) throws Exception {

        AppsV1Api api = new AppsV1Api(initApiClient(ip));
        
        V1Status status = api.deleteNamespacedReplicaSet(replicaSetName, namespace).execute();
        return toJSON(status);
    }
    
}

package skynet.platform.manager.k8s.service.impl;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.codehaus.plexus.util.cli.Commandline;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import skynet.boot.common.ShmDirUtils;
import skynet.boot.common.utils.MD5Util;
import skynet.platform.common.shell.Shell;
import skynet.platform.common.utils.PlaceholderReplacer;
import skynet.platform.common.utils.cmd.CommandLineExecutor;
import skynet.platform.manager.k8s.config.K8sConfigProperties;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class K8sKubectlService {

    private final File k8sTmpRoot;

    private final File k8sRuntimeRoot;
    private final K8sConfigProperties k8sConfigProperties;
    private final Map<String, KubeConfigFileItem> configFileMap;
    private String APPLY_CMD_TEMPLATE = "${kubectl_root}/${kubectl_file} apply -f ${file_name} --kubeconfig=${kube_config}";
    private String DELETE_CMD_TEMPLATE = "${kubectl_root}/${kubectl_file} delete -f ${file_name} --ignore-not-found=true --kubeconfig=${kube_config}";

    //kubectl --namespace=kube-system exec -it node-shell-923a770f-e10b-4c6b-9e28-a6152efffe1e -c turing-cloud-lb-beehive-paas -- sh
    private String POD_IT_TEMPLATE = "${kubectl_root}/${kubectl_file} exec --namespace=${namespace} --kubeconfig=${kube_config} -it ${pod_name}  -- sh";
    private String CONTAINER_IT_TEMPLATE = "${kubectl_root}/${kubectl_file} exec --namespace=${namespace} --kubeconfig=${kube_config} -it ${pod_name} -c ${container_name}  -- sh";
    private String POD_DEL_CMD_TEMPLATE = "${kubectl_root}/${kubectl_file} delete pod ${pod_name} --namespace=${namespace} --kubeconfig=${kube_config}";
    private String POD_LOGS_TEMPLATE = "${kubectl_root}/${kubectl_file} logs --tail=500 -f ${pod_name} --namespace=${namespace} --kubeconfig=${kube_config}";
    private String POD_LOGS_DOWNLOAD_TEMPLATE = "${kubectl_root}/${kubectl_file} logs ${pod_name} --since=48h --all-containers --namespace=${namespace} --kubeconfig=${kube_config}";
    private String CONTAINER_LOGS_TEMPLATE = "${kubectl_root}/${kubectl_file} logs --tail=200 -f ${pod_name} -c ${container_name} --namespace=${namespace} --kubeconfig=${kube_config}";
    private String DOCKER_SECRET_CREATE_TEMPLATE = "${kubectl_root}/${kubectl_file} create secret -n ${namespace} docker-registry skynet-docker-registry --docker-server=${registryUrl} --docker-username=${registryUsername} --docker-password=${registryPassword} --kubeconfig=${kube_config}";
    private String DOCKER_SECRET_DELETE_TEMPLATE = "${kubectl_root}/${kubectl_file} delete Secret skynet-docker-registry -n ${namespace} --kubeconfig=${kube_config}";
    private String DOCKER_SECRET_FETCH_TEMPLATE = "${kubectl_root}/${kubectl_file} get Secret -n ${namespace} | grep skynet-docker-registry --kubeconfig=${kube_config}";


    public K8sKubectlService(K8sConfigProperties k8sConfigProperties, Environment environment) throws IOException {
        this.k8sConfigProperties = k8sConfigProperties;
        this.k8sTmpRoot = new ShmDirUtils(environment).getShmDir("k8s");
        this.configFileMap = new ConcurrentHashMap<>();
        this.k8sRuntimeRoot = new File(k8sConfigProperties.getRuntimeRoot());
    }

    /**
     * 执行 kubectl apply -f xx.yaml 命令
     */
    public void apply(String ip, String kubeConfig, String yamlText) throws Exception {
        doKubectl(ip, kubeConfig, yamlText, APPLY_CMD_TEMPLATE);
    }

    /**
     * 执行 kubectl delete -f xx.yaml 命令
     */
    public void delete(String ip, String kubeConfig, String yamlText) throws Exception {
        doKubectl(ip, kubeConfig, yamlText, DELETE_CMD_TEMPLATE);
    }

    /**
     * 执行 kubectl apply -f xx.yaml 命令，模板文件支持动态参数替换
     */
    public void apply(String ip, String kubeConfig, String yamlTemplateFileName, Map<String, Object> params) throws Exception {
        String yamlText = getYamlText(ip, yamlTemplateFileName, params);
        this.apply(ip, kubeConfig, yamlText);
    }

    /**
     * 执行 kubectl delete -f xx.yaml 命令，模板文件支持动态参数替换
     */
    public void delete(String ip, String kubeConfig, String yamlTemplateFileName, Map<String, Object> params) throws Exception {
        String yamlText = getYamlText(ip, yamlTemplateFileName, params);
        this.delete(ip, kubeConfig, yamlText);
    }

    /**
     * 使用动态参数替换模板文件生成 Yaml 文件内容
     */
    private String getYamlText(String ip, String yamlTemplateFileName, Map<String, Object> params) throws Exception {
        Assert.hasText(ip, "ip not blank.");
        Assert.hasText(yamlTemplateFileName, "yamlTemplateFileName not blank.");
        log.debug("apply ip={}, yamlTemplateFileName={}, params={}", ip, yamlTemplateFileName, params);
        File yaml = new File(new File(k8sRuntimeRoot, "yaml"), yamlTemplateFileName.trim());
        String yamlText = FileUtils.readFileToString(yaml, StandardCharsets.UTF_8);
        if (params != null && !params.isEmpty()) {
            yamlText = PlaceholderReplacer.replace(yamlText, params);
        }
        return yamlText;
    }

    /**
     * 执行 kubectl 命令
     */
    private void doKubectl(String ip, String kubeConfig, String yamlText, String cmd) throws Exception {
        Assert.hasText(ip, "ip not blank.");
        Assert.hasText(yamlText, "yamlText not blank.");
        File yamlFile = File.createTempFile(String.format("%s-", ip), ".yaml", k8sTmpRoot);
        log.debug("yamlFile={}", yamlFile);
        log.trace(yamlText);
        FileUtils.writeStringToFile(yamlFile, yamlText, StandardCharsets.UTF_8);

        try {
            Map<String, Object> cmdParams = new HashMap<>();
            cmdParams.put("kube_config", getKubeConfigFile(ip, kubeConfig));
            cmdParams.put("file_name", yamlFile);
            cmdParams.put("kubectl_root", k8sRuntimeRoot);
            cmdParams.put("kubectl_file", getKubectlFile());
            String execCmd = PlaceholderReplacer.replace(cmd, cmdParams);
            this.exec(execCmd);
        } finally {
            FileUtils.deleteQuietly(yamlFile);
        }
    }

    public void delPod(String ip, String kubeConfig, String podName, String namespace) {
        Assert.hasText(ip, "ip not blank.");
        Assert.hasText(podName, "PodName not blank.");
        try {
            Map<String, Object> cmdParams = new HashMap<>();
            cmdParams.put("pod_name", podName);
            this.exec(applyBaseCmdParams(ip, kubeConfig, namespace, cmdParams, POD_DEL_CMD_TEMPLATE));
        } catch (Exception e) {
            log.error("Delete Pod Error={}", e.getMessage());
        }
    }

    /**
     * 创建 docker
     * @param ip ip
     * @param kubeConfig kubeConfig
     * @param namespace namespace
     * @param registryUrl registryUrl
     * @param registryUsername registryUsername
     * @param registryPassword registryPassword
     */
    public void createDockerSecret(String ip, String kubeConfig, String namespace, String registryUrl, String registryUsername, String registryPassword) {
        Assert.hasText(namespace, "namespace not blank.");
        Assert.hasText(registryUrl, "registryUrl not blank.");
        Assert.hasText(registryUsername, "registryUsername not blank.");
        Assert.hasText(registryPassword, "registryPassword not blank.");
        try {
            Map<String, Object> cmdParams = new HashMap<>();
            cmdParams.put("registryUrl", registryUrl);
            cmdParams.put("registryUsername", registryUsername);
            cmdParams.put("registryPassword", registryPassword);
            //先删除 后创建
            if(StringUtils.hasText(fetchDockerSecret(ip, kubeConfig, namespace))) {
                deleteDockerSecret(ip, kubeConfig, namespace);
            }
            this.exec(applyBaseCmdParams(ip, kubeConfig, namespace, cmdParams, DOCKER_SECRET_CREATE_TEMPLATE));
            log.info("create Docker Secret ip {}, namespace {} success", ip, namespace);
        } catch (Exception e) {
            log.error("create Docker Secret ip {}, namespace {} Error={}", ip, namespace, e.getMessage());
        }

    }

    public String fetchDockerSecret(String ip, String kubeConfig, String namespace) {
        Assert.hasText(namespace, "namespace not blank.");
        Assert.hasText(ip, "ip not blank.");
        Assert.hasText(kubeConfig, "kubeConfig not blank.");
        try {
            String fetchDockerSecretCmd = applyBaseCmdParams(ip, kubeConfig, namespace, new HashMap<>(), DOCKER_SECRET_FETCH_TEMPLATE);
            String fetchDockerSecretResult = Shell.build().execCmd(fetchDockerSecretCmd);
            log.info("{}, Result={}", fetchDockerSecretCmd, fetchDockerSecretResult);
            return fetchDockerSecretResult;
        } catch (Exception e) {
            log.error("fetch Docker Secret ip {}, namespace {} Error={}", ip, namespace, e.getMessage());
        }
        return null;
    }

    /**
     * 创建 docker
     * @param ip ip
     * @param kubeConfig kubeConfig
     * @param namespace namespace
     */
    public void deleteDockerSecret(String ip, String kubeConfig, String namespace) {
        Assert.hasText(namespace, "namespace not blank.");
        Assert.hasText(ip, "ip not blank.");
        Assert.hasText(kubeConfig, "kubeConfig not blank.");
        try {
            this.exec(applyBaseCmdParams(ip, kubeConfig, namespace, new HashMap<>(), DOCKER_SECRET_DELETE_TEMPLATE));
            log.info("Delete Docker Secret ip {}, namespace {} success", ip, namespace);
        } catch (Exception e) {
            log.error("Delete Docker Secret ip {}, namespace {} Error={}", ip, namespace, e.getMessage());
        }
    }

    /**
     *
     * @param ip ip
     * @param kubeConfig kubeConfig
     * @param namespace namespace
     * @param cmdParams cmdParams
     * @param containerDockerSecretTemplate containerDockerSecretTemplate
     */
    private String applyBaseCmdParams(String ip, String kubeConfig, String namespace, Map<String, Object> cmdParams, String containerDockerSecretTemplate) throws Exception {
        cmdParams.put("kube_config", getKubeConfigFile(ip, kubeConfig));
        cmdParams.put("namespace", StringUtils.hasText(namespace) ? namespace : k8sConfigProperties.getConsole().getDefaultNamespace());
        cmdParams.put("kubectl_root", k8sRuntimeRoot);
        cmdParams.put("kubectl_file", getKubectlFile());
        return PlaceholderReplacer.replace(containerDockerSecretTemplate, cmdParams);
    }

    /**
     * 获取 对进入Pod容器的交互命令
     *
     * @param ip ip
     * @param namespace namespace
     * @param podName podName
     * @param containerName containerName
     */
    public String getPodItCommand(String ip, String kubeConfig, String podName, String namespace, String containerName) throws Exception {
        return this.getCommand(ip, kubeConfig, podName, namespace, containerName, POD_IT_TEMPLATE, CONTAINER_IT_TEMPLATE);
    }

    public String getPodLogsCommand(String ip, String kubeConfig, String podName, String namespace, String containerName) throws Exception {
        return this.getCommand(ip, kubeConfig, podName, namespace, containerName, POD_LOGS_TEMPLATE, CONTAINER_LOGS_TEMPLATE);
    }

    public String downloadPodLogsCommand(String ip, String kubeConfig, String podName, String namespace) throws Exception {
        return this.getCommand(ip, kubeConfig, podName, namespace, null, POD_LOGS_DOWNLOAD_TEMPLATE, POD_LOGS_DOWNLOAD_TEMPLATE);
    }

    public String getCommand(String ip, String kubeConfig, String podName, String namespace, String containerName, String podTemplate, String containerTemplate) throws Exception {
        Assert.hasText(ip, "ip not blank.");
        Assert.hasText(kubeConfig, "kubeConfig not blank.");
        Assert.hasText(podName, "PodName not blank.");

        String template = podTemplate;
        Map<String, Object> cmdParams = new HashMap<>();
        cmdParams.put("ip", ip);
        cmdParams.put("pod_name", podName);
        cmdParams.put("kube_config", getKubeConfigFile(ip, kubeConfig));
        cmdParams.put("namespace", StringUtils.hasText(namespace) ? namespace : k8sConfigProperties.getConsole().getDefaultNamespace());
        cmdParams.put("kubectl_root", k8sRuntimeRoot);
        cmdParams.put("kubectl_file", getKubectlFile());
        if (StringUtils.hasText(containerName)) {
            cmdParams.put("container_name", containerName);
            template = containerTemplate;
        }
        return PlaceholderReplacer.replace(template, cmdParams);
    }

    /**
     * 根据当前操作系统的架构自动选择不同版本的 kubectl 可执行文件
     */
    private String getKubectlFile() {
        return String.format("kubectl-%s", k8sConfigProperties.getPlatform());
    }

    /**
     * Kubectl 目录位置
     *
     * @return
     */
    public File getK8sRuntimeRoot() {
        return k8sRuntimeRoot;
    }

    private File getKubeConfigFile(String ip, String kubeConfig) throws Exception {
        Assert.hasText(ip, "ip not blank.");

        String md5 = MD5Util.getMd5String(kubeConfig);
        if (!configFileMap.containsKey(ip) || md5.equalsIgnoreCase(configFileMap.get(ip).md5)) {
            File configFile = new File(k8sTmpRoot, String.format("%s.config", ip));
            log.debug("Create ip={} kubeConfigFile = {}", ip, configFile);
            FileUtils.writeStringToFile(configFile, kubeConfig, StandardCharsets.UTF_8);
            KubeConfigFileItem item = KubeConfigFileItem.builder().file(configFile).ip(ip).md5(md5).build();
            configFileMap.put(ip, item);
        }
        return configFileMap.get(ip).getFile();
    }


    private Integer exec(String cmdString) throws Exception {
        Commandline cmd = new Commandline(cmdString);
        log.debug("exec cmd-[{}]", cmd);
        CommandLineExecutor.executeCommandLine(cmd);
        return 0;
    }

    @Getter
    @Setter
    @Builder
    static class KubeConfigFileItem {
        private String ip;
        private String md5;
        private File file;
    }


    @Getter
@Setter
    @Builder
    static class CommandParam {
        private String ip;
        private String kubeConfig;
        private String podName;
        private String namespace;
        private String containerName;

        public void check() {
            Assert.hasText(ip, "ip not blank.");
            Assert.hasText(kubeConfig, "kubeConfig not blank.");
            Assert.hasText(podName, "PodName not blank.");
        }
    }
}

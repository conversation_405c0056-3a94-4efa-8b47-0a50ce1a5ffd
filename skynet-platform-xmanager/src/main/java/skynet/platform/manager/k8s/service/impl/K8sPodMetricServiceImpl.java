package skynet.platform.manager.k8s.service.impl;

import com.alibaba.fastjson2.JSONObject;
import io.kubernetes.client.custom.PodMetrics;
import io.kubernetes.client.custom.PodMetricsList;
import io.kubernetes.client.util.generic.GenericKubernetesApi;
import io.kubernetes.client.util.generic.options.ListOptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.manager.admin.v3.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sPodMetricService;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class K8sPodMetricServiceImpl extends K8sBaseService implements K8sPodMetricService {

    public K8sPodMetricServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 PodMetric 列表
     */
    @Override
    public List<JSONObject> getPodMetrics(String ip, K8sQuery k8sQuery) throws Exception {

        List<JSONObject> nodeMetricsDtoList = new ArrayList<>();
        GenericKubernetesApi<PodMetrics, PodMetricsList> metricsClient = new GenericKubernetesApi<>(
                PodMetrics.class,
                PodMetricsList.class,
                "metrics.k8s.io",
                "v1beta1",
                "pods",
                initApiClient(ip));
        ListOptions listOptions = new ListOptions();
        listOptions.setFieldSelector(k8sQuery.getFieldSelector());
        listOptions.setLabelSelector(k8sQuery.getLabelSelector());
        PodMetricsList nodeMetricsList = metricsClient.list(listOptions).throwsApiException().getObject();
        for (PodMetrics nodeMetrics : nodeMetricsList.getItems()) {
            nodeMetricsDtoList.add(toJSON(nodeMetrics));
        }
        return nodeMetricsDtoList;
    }

    /**
     * 获取 PodMetric 详情
     */
    @Override
    public JSONObject getPodMetric(String ip, String namespace, String nodeName) throws Exception {

        GenericKubernetesApi<PodMetrics, PodMetricsList> metricsClient = new GenericKubernetesApi<>(
                PodMetrics.class,
                PodMetricsList.class,
                "metrics.k8s.io",
                "v1beta1",
                "pods",
                initApiClient(ip));
        PodMetrics nodeMetrics = metricsClient.get(namespace, nodeName).throwsApiException().getObject();
        return toJSON(nodeMetrics);
    }

}

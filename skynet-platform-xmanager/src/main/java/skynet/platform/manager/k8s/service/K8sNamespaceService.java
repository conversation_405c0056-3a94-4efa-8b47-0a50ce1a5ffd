package skynet.platform.manager.k8s.service;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;

import skynet.platform.feign.model.K8sQuery;

public interface K8sNamespaceService {
    
    /**
     * 获取 Namespace 列表
     */
    List<JSONObject> getNamespaces(String ip, K8sQuery k8sQuery) throws Exception;

    /**
     * 获取 Namespace 详情
     */
    JSONObject getNamespace(String ip, String namespaceName) throws Exception;

    /**
     * 获取 Namespace Yaml
     */
    String getNamespaceYaml(String ip, String namespaceName) throws Exception;

    /**
     * 删除 Namespace
     */
    JSONObject deleteNamespace(String ip, String namespaceName) throws Exception;
}

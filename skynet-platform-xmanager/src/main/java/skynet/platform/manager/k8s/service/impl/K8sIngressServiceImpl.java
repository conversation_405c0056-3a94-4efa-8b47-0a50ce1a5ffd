package skynet.platform.manager.k8s.service.impl;

import com.alibaba.fastjson2.JSONObject;
import io.kubernetes.client.openapi.apis.NetworkingV1Api;
import io.kubernetes.client.openapi.models.V1Ingress;
import io.kubernetes.client.openapi.models.V1IngressList;
import io.kubernetes.client.openapi.models.V1Status;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.manager.admin.v3.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sIngressService;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class K8sIngressServiceImpl extends K8sBaseService implements K8sIngressService {

    public K8sIngressServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 Ingress 列表
     */
    @Override
    public List<JSONObject> getIngresss(String ip, K8sQuery k8sQuery) throws Exception {

        NetworkingV1Api api = new NetworkingV1Api(initApiClient(ip));

        List<JSONObject> ingressDtoList = new ArrayList<>();
        V1IngressList ingressList;
        if (StringUtils.isNotBlank(k8sQuery.getNamespace())) {
            ingressList = api.listNamespacedIngress(k8sQuery.getNamespace()).execute();
        } else {
            ingressList = api.listIngressForAllNamespaces().execute();
        }
        // 应用 K8sQuery 过滤条件
        List<V1Ingress> filteredIngresses = applyK8sQueryFilter(ingressList.getItems(), k8sQuery);

        for (V1Ingress ingress : filteredIngresses) {
            ingressDtoList.add(toJSON(ingress));
        }
        return ingressDtoList;
    }

    /**
     * 获取 Ingress 详情
     */
    @Override
    public JSONObject getIngress(String ip, String namespace, String ingressName) throws Exception {

        NetworkingV1Api api = new NetworkingV1Api(initApiClient(ip));

        V1Ingress ingress = api.readNamespacedIngress(ingressName, namespace).execute();
        return toJSON(ingress);
    }

    /**
     * 获取 Ingress Yaml
     */
    @Override
    public String getIngressYaml(String ip, String namespace, String ingressName) throws Exception {

        NetworkingV1Api api = new NetworkingV1Api(initApiClient(ip));

        V1Ingress ingress = api.readNamespacedIngress(ingressName, namespace).execute();
        return Yaml.dump(ingress);
    }

    /**
     * 删除 Ingress
     */
    @Override
    public JSONObject deleteIngress(String ip, String namespace, String ingressName) throws Exception {

        NetworkingV1Api api = new NetworkingV1Api(initApiClient(ip));

        V1Status status = api.deleteNamespacedIngress(ingressName, namespace).execute();
        return toJSON(status);
    }

}

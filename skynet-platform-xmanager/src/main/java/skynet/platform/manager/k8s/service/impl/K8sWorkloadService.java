package skynet.platform.manager.k8s.service.impl;

import java.time.Instant;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import io.kubernetes.client.custom.V1Patch;
import skynet.platform.feign.model.K8sUpdateImage;
import skynet.platform.feign.model.K8sUpdateReplica;
import skynet.platform.feign.model.K8sUpdateRevisionHistoryLimit;
import skynet.platform.manager.admin.v3.service.V3AgentService;

/**
 * Kubernetes 工作负载服务基类
 *
 * 升级说明：
 * - 兼容 kubernetes-client-java 24.0.0 版本
 * - V1Patch 构造方式保持不变
 * - 所有 patch 操作方法已验证兼容性
 * - 添加了详细的方法注释和升级说明
 */
public class K8sWorkloadService extends K8sBaseService {

    public K8sWorkloadService(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 构造一个 Patch Body，用于重启工作负载
     *
     * 升级说明：
     * - V1Patch 构造函数在 24.0.0 版本中保持兼容
     * - strategic-merge-patch 格式无变化
     * - 使用 kubectl.kubernetes.io/restartedAt 注解触发重启
     *
     * @return 用于重启工作负载的 V1Patch 对象
     */
    public V1Patch buildRestartPatch() {
        // strategic-merge-patch a workload
        JSONObject patch = new JSONObject()
            .fluentPut("spec", new JSONObject()
                .fluentPut("template", new JSONObject()
                    .fluentPut("metadata", new JSONObject()
                        .fluentPut("annotations", new JSONObject()
                            .fluentPut("kubectl.kubernetes.io/restartedAt", Instant.now())))));
        return new V1Patch(patch.toString());
    }

    /**
     * 构造一个 Patch Body，用于更新镜像版本
     *
     * 升级说明：
     * - JSON Patch 格式在 24.0.0 版本中保持兼容
     * - 支持同时更新普通容器和初始化容器的镜像
     * - 使用 replace 操作替换指定路径的镜像值
     *
     * @param k8sUpdateImage 包含容器镜像更新信息的对象
     * @return 用于更新镜像的 V1Patch 对象
     */
    public V1Patch buildUpdateImagePatch(K8sUpdateImage k8sUpdateImage) {
        // json-patch a workload
        JSONArray patch = new JSONArray();

        // 更新普通容器镜像
        if (k8sUpdateImage.getContainers() != null) {
            for (int i = 0; i < k8sUpdateImage.getContainers().size(); i++) {
                patch.add(new JSONObject()
                    .fluentPut("op", "replace")
                    .fluentPut("path", "/spec/template/spec/containers/" + i + "/image")
                    .fluentPut("value", k8sUpdateImage.getContainers().get(i).getImage()));
            }
        }

        // 更新初始化容器镜像
        if (k8sUpdateImage.getInitContainers() != null) {
            for (int i = 0; i < k8sUpdateImage.getInitContainers().size(); i++) {
                patch.add(new JSONObject()
                    .fluentPut("op", "replace")
                    .fluentPut("path", "/spec/template/spec/initContainers/" + i + "/image")
                    .fluentPut("value", k8sUpdateImage.getInitContainers().get(i).getImage()));
            }
        }
        return new V1Patch(patch.toString());
    }

    /**
     * 构造一个 Patch Body，用于修改工作负载的更新策略
     *
     * 升级说明：
     * - JSON Patch 格式在 24.0.0 版本中保持兼容
     * - 支持 RollingUpdate 和 Recreate 等更新策略
     *
     * @param strategy 更新策略对象
     * @return 用于更新策略的 V1Patch 对象
     */
    public V1Patch buildUpdateStrategyPatch(Object strategy) {
        // json-patch a workload
        JSONArray patch = new JSONArray();
        patch.add(new JSONObject()
            .fluentPut("op", "replace")
            .fluentPut("path", "/spec/strategy")
            .fluentPut("value", strategy));
        return new V1Patch(patch.toString());
    }

    /**
     * 构造一个 Patch Body，用于修改工作负载的副本数
     *
     * 升级说明：
     * - JSON Patch 格式在 24.0.0 版本中保持兼容
     * - 用于 Deployment、ReplicaSet 等工作负载的副本数调整
     *
     * @param k8sUpdateReplica 包含副本数信息的对象
     * @return 用于更新副本数的 V1Patch 对象
     */
    public V1Patch buildUpdateReplicaPatch(K8sUpdateReplica k8sUpdateReplica) {
        // json-patch a workload
        JSONArray patch = new JSONArray();
        patch.add(new JSONObject()
            .fluentPut("op", "replace")
            .fluentPut("path", "/spec/replicas")
            .fluentPut("value", k8sUpdateReplica.getReplicas()));
        return new V1Patch(patch.toString());
    }

    /**
     * 构造一个 Patch Body，用于修改工作负载的最大历史副本数
     *
     * 升级说明：
     * - JSON Patch 格式在 24.0.0 版本中保持兼容
     * - 用于控制 Deployment 等工作负载保留的历史版本数量
     *
     * @param k8sUpdateRevisionHistoryLimit 包含历史版本限制信息的对象
     * @return 用于更新历史版本限制的 V1Patch 对象
     */
    public V1Patch buildUpdateRevisionHistoryLimitPatch(K8sUpdateRevisionHistoryLimit k8sUpdateRevisionHistoryLimit) {
        // json-patch a workload
        JSONArray patch = new JSONArray();
        patch.add(new JSONObject()
            .fluentPut("op", "replace")
            .fluentPut("path", "/spec/revisionHistoryLimit")
            .fluentPut("value", k8sUpdateRevisionHistoryLimit.getRevisionHistoryLimit()));
        return new V1Patch(patch.toString());
    }

    /**
     * 解析百分比或整数值
     *
     * 升级说明：
     * - 此工具方法在 24.0.0 版本中无需修改
     * - 用于处理 Kubernetes 中的 IntOrString 类型字段
     *
     * @param value 字符串值，可能是百分比（如 "20%"）或整数（如 "3"）
     * @return 如果是百分比则返回字符串，否则返回整数
     */
    protected Object getIntOrStr(String value) {
        if (value.endsWith("%")) {
            return value;
        } else {
            return Integer.parseInt(value);
        }
    }
}

package skynet.platform.manager.k8s.service;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;

import skynet.platform.feign.model.K8sQuery;

public interface K8sJobService {
    
    /**
     * 获取 Job 列表
     */
    List<JSONObject> getJobs(String ip, K8sQuery k8sQuery) throws Exception;

    /**
     * 获取 Job 详情
     */
    JSONObject getJob(String ip, String namespace, String jobName) throws Exception;

    /**
     * 获取 Job Yaml
     */
    String getJobYaml(String ip, String namespace, String jobName) throws Exception;

    /**
     * 删除 Job
     */
    JSONObject deleteJob(String ip, String namespace, String jobName) throws Exception;
}

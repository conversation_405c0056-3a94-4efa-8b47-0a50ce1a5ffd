package skynet.platform.manager.k8s.controller;

import java.util.List;

import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSONObject;

import lombok.extern.slf4j.Slf4j;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3K8sCustomResourceDefinition;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.k8s.service.K8sCustomResourceDefinitionService;

@Slf4j
@RestController
@ExposeSwagger2
public class K8sCustomResourceDefinitionController implements V3K8sCustomResourceDefinition {
    
    private final K8sCustomResourceDefinitionService k8sCustomResourceDefinitionService;

    public K8sCustomResourceDefinitionController(K8sCustomResourceDefinitionService k8sCustomResourceDefinitionService) {
        this.k8sCustomResourceDefinitionService = k8sCustomResourceDefinitionService;
    }

    @Override
    public SkynetApiResponse<List<JSONObject>> getCustomResourceDefinitions(String ip, K8sQuery k8sQuery) throws Exception {
        SkynetApiResponse<List<JSONObject>> response = new SkynetApiResponse<>();
        List<JSONObject> results = k8sCustomResourceDefinitionService.getCustomResourceDefinitions(ip, k8sQuery);
        response.setData(results);
        log.debug("getCustomResourceDefinitions response:{}", response);
        return response;
    }

    @Override
    public SkynetApiResponse<JSONObject> getCustomResourceDefinition(String ip, String crdName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sCustomResourceDefinitionService.getCustomResourceDefinition(ip, crdName);
        response.setData(result);
        log.debug("getCustomResourceDefinition response:{}", response);
        return response;
    }

    @Override
    public SkynetApiResponse<String> getCustomResourceDefinitionYaml(String ip, String crdName) throws Exception {
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        String result = k8sCustomResourceDefinitionService.getCustomResourceDefinitionYaml(ip, crdName);
        response.setData(result);
        log.debug("getCustomResourceDefinitionYaml response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S CRD 管理", operation = "删除 CRD", message = "ip=#{#ip}, crdName=#{#crdName}")
    public SkynetApiResponse<JSONObject> deleteCustomResourceDefinition(String ip, String crdName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sCustomResourceDefinitionService.deleteCustomResourceDefinition(ip, crdName);
        response.setData(result);
        log.debug("deleteCustomResourceDefinition response:{}", response);
        return response;
    }
    
}

package skynet.platform.common.shell;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.unit.DataSize;
import oshi.SystemInfo;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.software.os.OperatingSystem;
import skynet.platform.common.xray.MetricSampler;
import skynet.platform.common.xray.domain.ConnectionStat;
import skynet.platform.common.xray.domain.DiskStat;
import skynet.platform.common.xray.domain.MemStat;
import skynet.platform.common.xray.domain.NetworkStat;

import java.time.Instant;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/11 11:05 上午
 */
@Slf4j
public class MetricSamplerTest {
    public static void main(String[] args) {

        SystemInfo systemInfo = new SystemInfo();
        OperatingSystem os = systemInfo.getOperatingSystem();
        HardwareAbstractionLayer hw = systemInfo.getHardware();


//        StopWatch stopWatch = new StopWatch();
//        OSProcess process = os.getProcess(8);
//        stopWatch.start();
//        //  System.out.println(process);
//        for (int i = 0; i < 1; i++) {
//            process = os.getProcess(2632);
//            List<OSProcess> list = os.getChildProcesses(2632, null, null, 1000);
//            System.out.println("child :" + list.size());
//        }
//        stopWatch.stop();
////        System.out.println(process);
//        System.out.printf((stopWatch.getLastTaskTimeMillis()) + "");

        //os.getNetworkParams();
//
//        ConnectionStat connectionStat = new ConnectionStat();
//        // connectionStat.setAllInboundTotal();
//        log.info(JSON.toJSONString(connectionStat));


        MetricSampler metricSampler = new MetricSampler();

        MemStat memStat = metricSampler.memStat();
        System.out.println(JSON.toJSONString(memStat));
        System.out.println("----------------------------");

        ConnectionStat connectionStat = metricSampler.connectionStat();
        System.out.println(JSON.toJSONString(connectionStat));
        System.out.println("----------------------------");

        List<NetworkStat> networkStatList = metricSampler.networkStat(true);
        System.out.println(JSON.toJSONString(networkStatList));
        System.out.println("----------------------------");


        List<DiskStat> diskStatList = metricSampler.diskStat(null);
        System.out.println(JSON.toJSONString(diskStatList));
    }


    private static String durationConvert(Long dateTime) {
        if (dateTime > 60) {
            if (dateTime > 3600) {
                if (dateTime > 86400) {
                    return dateTime / 86400 + "天" + durationConvert(dateTime % 86400);
                } else {
                    return dateTime / 3600 + "时" + durationConvert(dateTime % 3600);
                }
            } else {
                return dateTime / 60 + "分" + durationConvert(dateTime % 60);
            }
        } else {
            return dateTime + "秒";
        }
    }
}

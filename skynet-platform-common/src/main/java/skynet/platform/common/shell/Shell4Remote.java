package skynet.platform.common.shell;

import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.io.*;


/**
 * 操作远程 shell 类
 *
 * <AUTHOR>
 */

@Slf4j
@SuppressWarnings("resource")
class Shell4Remote implements Shell {
    private final String ip;
    private final int port;
    private final String username;
    private final String password;
    private final int timeout;

    private ChannelSftp sftp;
    private Session session;

    protected Shell4Remote(String ip, int port, String username, String password, int sshTimeoutSec) throws Exception {
        Assert.hasText(ip, "The IP is blank.");
        Assert.hasText(username, "The username is blank.");
        Assert.hasText(password, "The password is blank.");

        this.ip = ip;
        this.port = port;
        this.username = username;
        this.password = password;
        this.timeout = sshTimeoutSec;
        connect();
        log.debug("remote shell created. ip={},port={},username={}", ip, port, username);
    }

    private void connect() throws Exception {
        //init sftp
        JSch jsch = new JSch();
        session = (port <= 0) ? jsch.getSession(username, ip) : jsch.getSession(username, ip, port);
        // 设置登陆主机的密码
        session.setPassword(password);
        // 优先使用 password 验证   注：session.connect()性能低，使用password验证可跳过gssapi认证，提升连接服务器速度
        session.setConfig("PreferredAuthentications", "password");
        // 设置第一次登陆的时候提示，可选值：(ask | yes | no)
        session.setConfig("StrictHostKeyChecking", "no");

        session.connect((this.timeout <= 0 ? 10 : this.timeout) * 1000);

        sftp = (ChannelSftp) session.openChannel("sftp");
        sftp.connect(15 * 1000);
    }

    @Override
    public String execCmd(String... commands) throws Exception {
        if (commands == null) {
            throw new Exception("commands is null");
        }

        ChannelShell channelShell = (ChannelShell) session.openChannel("shell");
        log.debug("Connect {} by {} ...", ip, username);
        channelShell.connect(15 * 1000);
        StringBuilder result = new StringBuilder();

        try (InputStream inputStream = channelShell.getInputStream();
             OutputStream outputStream = channelShell.getOutputStream()) {

            // 发送需要执行的 SHELL 命令，需要用 \n 结尾，表示回车
            for (String cmd : commands) {
                String shellCmd = String.format("%s %s", cmd, System.lineSeparator());
                outputStream.write(shellCmd.getBytes());
            }

            outputStream.write(String.format("%s %s", "exit", System.lineSeparator()).getBytes());
            outputStream.flush();

            try (BufferedReader in = new BufferedReader(new InputStreamReader(inputStream))) {
                String msg;
                while ((msg = in.readLine()) != null) {
                    log.debug(msg);
                    result.append(String.format("%s%s", msg, System.lineSeparator()));
                }
            }
        } catch (Exception ex) {
            log.error("执行shell出错", ex);
        } finally {
            channelShell.disconnect();
            log.debug("关闭shell");
        }
        return result.toString();
    }

    @Override
    public boolean copy(File srcFile, String absoluteDestDir) throws Exception {
        log.debug("copy file {} => {}", srcFile, absoluteDestDir);
        if (srcFile == null || absoluteDestDir == null) {
            throw new NullPointerException("srcFile or dst is null");
        }
        if (!srcFile.exists()) {
            throw new FileNotFoundException(String.format("srcFile doesn't exist. srcFile.path=%s", srcFile.getAbsolutePath()));
        }
        mkdir(absoluteDestDir);
        return copyFile(srcFile, absoluteDestDir);
    }

    @Override
    public boolean isFileExist(String absoluteFilePath) throws Exception {
        return exists(absoluteFilePath);
    }

    @Override
    public boolean isDirExist(String absoluteDirPath) throws Exception {
        return exists(absoluteDirPath);
    }

    @Override
    public boolean mkdir(String absoluteDirPath) throws Exception {
        log.debug("mkdir {}", absoluteDirPath);
        if (isDirExist(absoluteDirPath)) {
            sftp.cd(absoluteDirPath);
            return true;
        }
        String[] pathArray = absoluteDirPath.split("/");
        StringBuilder filePathSb = new StringBuilder("/");
        for (String path : pathArray) {
            if ("".equals(path)) {
                continue;
            }
            filePathSb.append(path).append("/");
            String filePath = filePathSb.toString();
            if (!isDirExist(filePath)) {
                sftp.mkdir(filePath);
            }
            sftp.cd(filePath);
        }
        sftp.cd(absoluteDirPath);
        return true;
    }

    @Override
    public String readFile(String absoluteFilePath) throws Exception {
        log.debug("ReadFile. file.path={}", absoluteFilePath);
        if (isDir(absoluteFilePath)) {
            throw new Exception(String.format("file is a directory. filePath=%s", absoluteFilePath));
        }

        StringBuilder result = new StringBuilder();
        try (InputStream in = sftp.get(absoluteFilePath)) {
            try (BufferedReader br = new BufferedReader(new InputStreamReader(in))) {
                String msg;
                while ((msg = br.readLine()) != null) {
                    log.debug("sftp.get msg={}", msg);
                    result.append(msg).append(System.lineSeparator());
                }
            }
        }
        log.trace("ReadFile File.path={}; contex={}", absoluteFilePath, result);
        return result.toString();
    }

    private boolean isDir(String path) throws Exception {
        if (StringUtils.isBlank(path)) {
            throw new Exception(String.format("exists. path is blank. ip=%s,port=%s,path=[%s]", ip, port, path));
        }
        try {
            SftpATTRS sftpAttrs = sftp.lstat(path);
            return sftpAttrs.isDir();
        } catch (SftpException e) {
            if (e.id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                return false;
            } else {
                throw e;
            }
        }
    }

    private boolean exists(String path) throws Exception {
        if (StringUtils.isBlank(path)) {
            throw new Exception(String.format("exists. path is blank. ip=%s,port=%s,path=[%s]", ip, port, path));
        }
        boolean fileExist = true;
        try {
            sftp.lstat(path);
        } catch (SftpException e) {
            if (e.id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                // file doesn't exist
                fileExist = false;
            } else {
                // something else went wrong
                throw e;
            }
        }
        return fileExist;
    }

    /**
     * 把源文件/文件夹 srcFile 拷贝到目的路径 absoluteDst 下
     * 拷贝完成后 absoluteDst 是 srcFile 副本的父目录
     *
     * @param srcFile     源文件
     * @param absoluteDst 目的地址，绝对路径
     * @return 拷贝成功返回 true 否则返回 false
     */
    private boolean copyFile(File srcFile, String absoluteDst) {
        log.debug("copyFile file {} => {}", srcFile, absoluteDst);

        if (srcFile.isDirectory()) {
            String srcFileName = srcFile.getName();
            try {
                sftp.cd(absoluteDst);
                log.info("making dir:{}/{}", sftp.pwd(), srcFileName);
                sftp.mkdir(srcFileName);
                log.info("mkdir success:{}/{}", sftp.pwd(), srcFileName);
            } catch (Exception e) {
                log.error(String.format("mkdir failed. ip=%s,port=%s,dirName=%s/%s", ip, port, absoluteDst, srcFileName), e);
                return false;
            }
            absoluteDst = absoluteDst + File.separator + srcFileName;
            try {
                sftp.cd(srcFileName);
            } catch (SftpException e) {
                log.error(String.format("sftp.cd err. ip=%s,port=%s,srcFileName=%s", ip, port, srcFileName), e);
                return false;
            }
            File[] list = srcFile.listFiles();
            if (list == null || list.length == 0) {
                return true;
            }
            for (File f : list) {
                boolean copyOk = copyFile(f, absoluteDst);
                if (!copyOk) {
                    return false;
                }
            }
            return true;
        } else {
            try {
                sftp.cd(absoluteDst);
            } catch (SftpException e) {
                log.error(String.format("sftp.cd err. ip=%s,port=%s,absoluteDst=%s", ip, port, absoluteDst), e);
                return false;
            }
            log.info("copying file={}", srcFile.getAbsolutePath());
            try (OutputStream out = sftp.put(srcFile.getName()); InputStream in = new FileInputStream(srcFile)) {
                byte[] b = new byte[1024];
                int n;
                while ((n = in.read(b)) != -1) {
                    out.write(b, 0, n);
                }
                out.flush();
            } catch (SftpException | IOException e) {
                log.error(String.format("sftp.put err. ip=%s,port=%s,absoluteDst=%s,srcFile.path=%s", ip, port, absoluteDst
                        , srcFile.getAbsolutePath()), e);
                return false;
            }
            return true;
        }
    }

    @Override
    public void close() throws Exception {
        if (sftp != null) {
            sftp.exit();
            sftp = null;
        }
        if (session != null) {
            session.disconnect();
            session = null;
        }
    }
}

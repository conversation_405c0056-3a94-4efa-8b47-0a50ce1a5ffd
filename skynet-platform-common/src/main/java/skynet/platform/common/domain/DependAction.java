package skynet.platform.common.domain;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.common.domain.Jsonable;

/**
 * 依赖服务项
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class DependAction extends Jsonable {

    /**
     * 服务坐标编码  如：  mysql@turing-pass
     */
    @Schema(name = "服务坐标编码") //, position = 10
    private String code;

    /**
     * 依赖类型
     */
    @Schema(name = "依赖类型，默认运行依赖") //, position = 20
    private DependType type = DependType.runtime;

    public enum DependType {
        /**
         * 运行依赖
         */
        runtime,

        /**
         * 调用依赖
         */
        calling,
    }
}

package skynet.platform.common.domain;

import com.alibaba.fastjson2.annotation.J<PERSON>NField;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

/**
 * GpuItem
 *
 * <AUTHOR> 2020年11月26日20:54:31
 */
@Getter
@Setter
public class GpuItem extends J<PERSON>able {
    @J<PERSON><PERSON>ield(ordinal = 10)
    private String name;
    @J<PERSON><PERSON>ield(ordinal = 20)
    private String index;
    @JSO<PERSON>ield(ordinal = 30)
    private String memory;

    public GpuItem() {
    }

    public GpuItem(String index, String name, String memory) {
        this.index = index;
        this.name = name;
        this.memory = memory;
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
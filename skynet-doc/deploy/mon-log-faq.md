# 监控和日志常见问题

## 采集服务问题

### Prometheus 没有任何采集数据

检查 `Skynet监控-管理中心` 是否启动，并且和 `Skynet监控-采集服务` 运行在同一台机器上。如果 `Skynet监控-管理中心` 是以 DockerBoot 方式启动的，检查 `Skynet监控-采集服务` 的配置文件是否正确地挂载到 `Skynet监控-管理中心` 的容器中。

如果服务定义和部署都正常，检查管理中心的日志，看下是否存在如下报错：

![](./asserts/config-center-zk.png)

这是由于管理中心在容器中运行，无法访问 127.0.0.1:2181 这个 ZK 地址，修改 Skynet 配置文件，将 ZK 地址改成服务器的 IP 地址即可。

## 仪表盘问题

### 仪表盘所有的监控图表全部显示为空

进入 `Skynet监控-采集服务` 服务首页，点击 `Status` -> `Targets` 页面，确保 Prometheus 抓取指标正常，如果该列表为空，根据上一节内容进行排查。

### Grafana 没有数据源

检查仪表盘的服务坐标是否为 `grafana-server-v6@ant-mon`，采集服务的坐标是否为 `prometheus@ant-mon`，如果不是，可以在 `基础服务 [ant]` 的系统级属性中新增下面的配置（修改为你的坐标）：

```properties
skynet.grafana.actionPoint=grafana-server-v6@ant-mon
skynet.prometheus.actionPoint=prometheus@ant-mon
```

然后重启该机器上的 Skynet Agent 即可。

## 日志问题

### 不显示服务日志

如果服务已经运行有一段时间了，但是突然某天开始不显示日志，那么很有可能是服务器上的日志文件被人工删除了，导致 Skynet 无法写入日志文件，解决办法：重启 Skynet Agent。

如果服务启动后服务日志就不显示的话，判断服务是否正常运行，比如看看服务端口是否能访问，分两种情况：

#### 1. 服务端口不能访问

说明服务在启动的时候卡住了，通过 `jstack` 把服务的线程堆栈导出来，分析服务卡在哪个地方。大概率是卡在 DNS 解析，参考 Skynet 部署文档，检查 `/etc/hosts` 和 `/etc/resolv.conf` 等文件配置。

#### 2. 服务端口能访问

说明服务本身没有问题，大概率是出在服务的日志配置上，检查服务是否有自己的日志配置，比如 Java 服务是否内置了 `logback.xml` 文件，将日志输出到指定的位置了。有两种解决方法，一，去掉程序中的日志配置，直接将日志打到控制台，二，修改日志配置，将日志输出到控制台。Skynet 只能抓到输出到控制台的日志，抓不到你自定义的日志文件。

### Skynet 如何配置日志保存时间？

Spring Boot 默认情况下不支持配置日志保存时间，它的日志文件是按大小滚动生成的，只支持设置日志文件的个数和单个日志文件的大小：

```
# 每个日志文件最多 20M
logging.logback.rollingpolicy.max-file-size=20M

# 最多生成 7 个日志文件
logging.logback.rollingpolicy.max-history=7
```

不过，由于 Skynet 的日志文件是以日期命名的，所以也是按天滚动的，只要每个文件的大小足够大，就可以实现类似保存时间的效果：

```
# 每个日志文件最多 10G
logging.logback.rollingpolicy.max-file-size=10G

# 最多生成 7 个日志文件
logging.logback.rollingpolicy.max-history=7
```

只要单日日志文件大小不超过 10G，那么 `max-history` 就可以认为是 7 天。

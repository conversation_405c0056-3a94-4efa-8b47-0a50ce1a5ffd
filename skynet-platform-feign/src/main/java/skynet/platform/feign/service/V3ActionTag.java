package skynet.platform.feign.service;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.annotations.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.SkynetApiResponse;

import java.util.List;

/**
 * <AUTHOR> by jianwu6 on 2020/8/18 16:56
 */
@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3ActionTag")
@Tag(name = "v3. 服务标签管理", description = "服务标签")//, hidden = true)
public interface V3ActionTag {

    String PREFIX = "/skynet/api/v3/action-tags";

    @GetMapping(value = PREFIX, produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取所有标签")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<List<String>> getTags();

    @PutMapping(value = PREFIX, produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "更新标签列表")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<Void> updateTags(@RequestBody List<String> tags);
}

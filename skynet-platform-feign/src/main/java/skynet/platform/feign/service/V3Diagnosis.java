package skynet.platform.feign.service;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.DiagnosisResponse;
import skynet.platform.feign.model.SkynetApiResponse;

import java.util.List;

/**
 * 系统诊断，如 网络配置，磁盘状态，内存状态，端口占用，日志文件等
 *
 * <AUTHOR>
 * @date 2020/12/4 17:34
 */
@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3Diagnosis")
@Tag(name = "v3. 服务器诊断", description = "服务器节点相关的诊断报告")//, hidden = true)
public interface V3Diagnosis {

    String PREFIX = "/skynet/api/v3/diagnosis";

    @GetMapping(value = PREFIX + "/list", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取诊断项目列表")
        //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<List<String>> getGroups() throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/{groupIndex}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取诊断报告")
        //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<List<DiagnosisResponse>> getReport(@Parameter(description = "服务器IP") @PathVariable String ip, @PathVariable @Parameter(description = "分组序号") int groupIndex) throws Exception;
}

package skynet.platform.feign.model.xray;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DiskStat extends PerfStatBase {

    public static final String METRIC_TYPE = "x-disk";

    @Schema(title = "dev")
    private String devName;

    @Schema(title = "mountpoint")
    private String dirName;

    @Schema(title = "fstype")
    private String fsType;

    @Schema(title = "xray_disk_capacity_total")
    private long capacityTotal;

    @Schema(title = "xray_disk_capacity_free")
    private long capacityFree;

    @Schema(title = "xray_disk_capacity_used")
    private long capacityUsed;

    @Schema(title = "xray_disk_capacity_avail")
    private long capacityAvail;

    @Schema(title = "xray_disk_capacity_used_perc")
    private double capacityUsePercent;

    @Schema(title = "xray_disk_files")
    private long files;

    @Schema(title = "xray_disk_free_files")
    private long freeFiles;

    @Schema(title = "xray_disk_reads")
    private long reads;

    @Schema(title = "xray_disk_writes")
    private long writes;

    @Schema(title = "xray_disk_read_bytes")
    private long readBytes;

    @Schema(title = "xray_disk_write_bytes")
    private long writeBytes;

    @Schema(title = "xray_disk_queue")
    private double queue;

    @Schema(title = "xray_disk_srv_time")
    private double serviceTime;

    @Schema(title = "xray_disk_r_per_sec")
    private long readPerSec;

    @Schema(title = "xray_disk_w_per_sec")
    private long writePerSec;

    @Schema(title = "xray_disk_r_bytes_per_sec")
    private long readBytesPerSec;

    @Schema(title = "xray_disk_w_bytes_per_sec")
    private long writeBytesPerSec;

    @Override
    public String toString() {
        return super.toString();
    }

}

package skynet.platform.feign.model;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

@Getter
@Setter
@Schema(title =  "集群信息")
public class ClusterInfoDto extends Jsonable {

    @Schema(title = "集群名称")//, position = 10)
    private String clusterName;

    @Schema(title = "zookeeper服务器列表，用逗号隔开")//, position = 20)
    private String zkServers;

    @Schema(title = "skynet部署路径")//, position = 40)
    private String skynetHome;

    @Override
    public String toString() {
        return super.toString();
    }
}

package skynet.platform.agent.grafana.service;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import skynet.platform.agent.grafana.annotation.ConditionOnGrafana;
import skynet.platform.agent.grafana.exception.TraceableRestClientException;
import skynet.platform.agent.server.AntServerApp;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.AntActionStatus;

import jakarta.annotation.PostConstruct;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.LongAdder;

@Slf4j
@ConditionOnGrafana
@Configuration
public class QueryCache {

    private static final String KEY_IP_PORT = "KEY_IP_PORT";

    /**
     * 线程池队列大小
     */
    @Value("${skynet.grafana.query.pool.qsize:32}")
    private final int qsize = 32;

    /**
     * 线程池最大线程数
     */
    @Value("${skynet.grafana.query.threads.max:16}")
    private final int threadsMax = 16;

    /**
     * 线程池核心线程数
     */
    @Value("${skynet.grafana.query.threads.core:8}")
    private final int threadsCore = 8;

    /**
     * 缓存过期时间
     */
    @Value("${skynet.grafana.query.expire:5}")
    private final int expiration = 5;

    /**
     * 缓存加载IO超时时间
     */
    @Value("${skynet.grafana.query.timeout:10}")
    private final int timeout = 10;

    @Autowired
    private IAntConfigService antConfigService;

    @Autowired
    @Qualifier(RestTemplateBeanConfig.BEAN_NAME)
    private RestTemplate restTemplate;

    private ExecutorService threadPool;

    /**
     * 所有Server的IP端口映射缓存
     */
    private LoadingCache<String, Map<String, Integer>> serverIpPortCache;

    /**
     * 所有Server资源信息查询结果的缓存
     */
    private LoadingCache<String, String> queryResultCache;

    @PostConstruct
    public void init() {
        ThreadFactory threadFactory = new ThreadFactory() {
            private final LongAdder count = new LongAdder();

            @Override
            public Thread newThread(Runnable r) {
                count.increment();
                return new Thread(r, "GrafanaQueryCacheLoaderPool-" + count.intValue());
            }
        };
        RejectedExecutionHandler handler = (r, executor) -> log.warn("fail to add task in GrafanaQueryCacheLoaderPool");
        this.threadPool = new ThreadPoolExecutor(threadsCore, threadsMax, 30, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(qsize), threadFactory, handler);

        CacheLoader<String, Map<String, Integer>> serverIpPortLoader = new CacheLoader<String, Map<String, Integer>>() {

            @Override
            public Map<String, Integer> load(String arg0) throws Exception {
                Callable<Map<String, Integer>> task = () -> {
                    Map<String, AntActionStatus> onlineNodes = antConfigService.getOnlineActionNodeStatus(AntServerApp.ACTION_POINT, null);
                    Map<String, Integer> ret = new HashMap<>(onlineNodes.size());
                    for (AntActionStatus nodeState : onlineNodes.values()) {
                        //排除 kubernetes agent
                        if (!"kubernetes".equalsIgnoreCase(nodeState.getType())) {
                            ret.put(nodeState.getIp(), nodeState.getPort());
                        }
                    }
                    return ret;
                };
                Future<Map<String, Integer>> future = threadPool.submit(task);
                try {
                    return future.get(timeout, TimeUnit.SECONDS);
                } catch (TimeoutException e) {
                    future.cancel(true);
                    throw e;
                }
            }
        };

        this.serverIpPortCache = CacheBuilder.newBuilder().expireAfterWrite(expiration * 2, TimeUnit.SECONDS)
                .build(CacheLoader.asyncReloading(serverIpPortLoader, this.threadPool));

        CacheLoader<String, String> queryResultLoader = new CacheLoader<String, String>() {

            @Override
            public String load(String arg0) throws Exception {
                log.debug("load {}", arg0);
                Callable<String> task = () -> {
                    String ret;
                    try {
                        ResponseEntity<String> entity = restTemplate.getForEntity(new URI(arg0), String.class);
                        ret = entity.getBody();
                        log.trace("GET {} return \n {}", arg0, ret);
                    } catch (RestClientException e) {
                        throw TraceableRestClientException.build("GET", arg0, e);
                    } catch (URISyntaxException e) {
                        throw new RuntimeException(e);
                    }
                    return ret;
                };
                Future<String> future = threadPool.submit(task);
                try {
                    return future.get(timeout, TimeUnit.SECONDS);
                } catch (TimeoutException e) {
                    future.cancel(true);
                    throw e;
                }
            }
        };

        this.queryResultCache = CacheBuilder.newBuilder().expireAfterWrite(expiration, TimeUnit.SECONDS)
                .build(queryResultLoader);
    }

    public Map<String, Integer> getAllIpPort() throws Exception {
        try {
            return this.serverIpPortCache.get(KEY_IP_PORT);
        } catch (ExecutionException e) {
            throw new Exception(e.getCause());
        }
    }

    public String query(String uri) throws Exception {
        try {
            return this.queryResultCache.get(uri);
        } catch (ExecutionException e) {
            throw new Exception(e.getCause());
        }
    }

}

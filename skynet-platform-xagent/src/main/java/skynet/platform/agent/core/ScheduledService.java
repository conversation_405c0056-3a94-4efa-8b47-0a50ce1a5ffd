package skynet.platform.agent.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import skynet.platform.agent.core.config.AgentProperties;
import skynet.platform.agent.core.core.EventService;
import skynet.platform.common.domain.BootProfile;
import skynet.platform.common.domain.BootStatus;
import skynet.platform.common.repository.domain.AntNodeUpType;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 计划服务
 *
 * <pre>
 * Action计划 启动、停止、重新加载、定时守护
 *
 * </pre>
 *
 * <AUTHOR> [2018年7月7日 下午11:40:41]
 */
@Slf4j
@Service
public class ScheduledService implements AutoCloseable {

    private final AgentProperties agentProperties;
    private final BootService bootService;
    private final EventService eventService;

    private volatile boolean isStarted = false;

    private volatile Map<String, BootProfile> scheduledBootMap = new HashMap<>(0);
    private ScheduledExecutorService es;

    public ScheduledService(AgentProperties agentProperties, BootService bootService, EventService eventService) {
        this.agentProperties = agentProperties;
        this.bootService = bootService;
        this.eventService = eventService;
    }

    /**
     * 集合减
     *
     * @param source
     * @param target
     * @return
     */
    private static LinkedHashMap<String, BootProfile> subtractMap(Map<String, BootProfile> source, Map<String, BootProfile> target) {
        LinkedHashMap<String, BootProfile> map = new LinkedHashMap<>();
        for (Entry<String, BootProfile> item : source.entrySet()) {
            if (!target.containsKey(item.getKey())) {
                map.put(item.getKey(), item.getValue());
            }
        }
        return map;
    }

    @PostConstruct
    private void init() throws Exception {
        // 启动定时器
        es = Executors.newScheduledThreadPool(1, runnable -> {
            Thread thread = new Thread(runnable);
            thread.setName("ant.scheduler");
            return thread;
        });
        es.scheduleWithFixedDelay(() -> {
            try {
                if (isStarted) {
                    refresh();
                }
            } catch (Exception e) {
                log.error(String.format("Refresh Error: %s ", e.getMessage()), e);
            }
        }, Math.max(agentProperties.getInitialDelay(), 10), Math.max(agentProperties.getDelay(), 15), TimeUnit.SECONDS);
    }

    private void refresh() throws Exception {

        long cost = System.currentTimeMillis();
        // 检查文件存储，把已经停止的进程状态清理掉
        this.bootService.cleanBootStatus();

        // 清理多余的进程 按计划启动进程
        this.load(scheduledBootMap);

        log.debug("Refresh cost={} ms", (System.currentTimeMillis() - cost));
        // TODO: 未考虑 状态文件被删除。可以从zk状态表中恢复
    }

    public void start(Map<String, BootProfile> scheduledBootMap) throws IOException {
        ///
        log.info("Start boot size={}", scheduledBootMap.size());
        this.scheduledBootMap = scheduledBootMap;
        this.load(scheduledBootMap);
        this.isStarted = true;
    }

    public void stop(String aid) throws Exception {
        Assert.hasText(aid, "The aid is blank.");
        bootService.stop(aid.trim());
    }

    /**
     * 如果PId 为 0 将 停止所有的节点
     *
     * @param pid
     * @throws Exception
     */
    public void stop(long pid) throws Exception {
        bootService.stop(pid);
    }

    /**
     * 停止所有的boot
     *
     * @throws Exception
     */
    public void stopAll() throws Exception {
        this.start(new HashMap<>(0));
    }

    @Override
    public void close() throws Exception {
        if (es != null) {
            es.shutdownNow();
        }
        // xagent停止，不停止托管的服务
        //this.stopAll();
    }

    /**
     * 计划与在线进程的比对：
     * <p>
     * 停止多余的进程 按计划启动进程
     *
     * @param scheduledBootMap
     * @throws IOException
     */
    private synchronized void load(Map<String, BootProfile> scheduledBootMap) throws IOException {

        Map<String, BootStatus> runningBoot = bootService.getAllBoot();
        //恢复BootRunner
        for (BootStatus bootStatus : runningBoot.values()) {
            try {
                log.debug("RestoreRunner.[{}]", bootStatus.getAid());
                bootService.restoreRunner(bootStatus.getProfile());
            } catch (Exception e) {
                log.error("RestoreRunner [{}] err={}.", bootStatus.getAid(), e.getMessage());
            }
        }

        LinkedHashMap<String, BootProfile> onlineBootMap = new LinkedHashMap<>();
        for (Entry<String, BootStatus> item : runningBoot.entrySet()) {
            onlineBootMap.put(item.getKey(), item.getValue().getProfile());
        }

        log.debug("ScheduledList  [{}]: \t{}", scheduledBootMap.size(), scheduledBootMap.keySet());
        log.debug("OnlineBootList [{}]: \t{}", onlineBootMap.size(), onlineBootMap.keySet());
        LinkedHashMap<String, BootProfile> newIndexActionMap = subtractMap(scheduledBootMap, onlineBootMap);
        LinkedHashMap<String, BootProfile> stopIndexActionMap = subtractMap(onlineBootMap, scheduledBootMap);

        // 先停止
        for (Entry<String, BootProfile> item : stopIndexActionMap.entrySet()) {
            try {
                bootService.stop(item.getKey());
                runningBoot.remove(item.getKey());
                // 报告事件
                eventService.putKillProcessEvent(item.getKey(), item.getValue().getPid());
            } catch (Exception e) {
                log.error("stop action [{}]. error={}", item.getKey(), e.getMessage(), e);
            }
        }

        // 已经run，但掉线的并且启动次数少于3的，还需要再次启动
        for (Entry<String, BootStatus> item : runningBoot.entrySet()) {
            if (item.getValue().getState().getUp() == AntNodeUpType.DOWN && item.getValue().getBootIndex() < agentProperties.getTrySize()) {
                log.info("Add reboot action:\t{}", item.getKey());
                newIndexActionMap.put(item.getKey(), item.getValue().getProfile());
            }
        }
        if (!newIndexActionMap.isEmpty() || !stopIndexActionMap.isEmpty()) {
            log.info("StartBootList:\t{}", newIndexActionMap.keySet());
            log.info("StopBootList:\t{}", stopIndexActionMap.keySet());
        }

        AtomicInteger delayCount = new AtomicInteger(agentProperties.getLoadDelaySize());
        // 再启动
        for (BootProfile bootProfile : newIndexActionMap.values()) {
            try {
                BootStatus bootStatus = bootService.start(bootProfile);
                // 报告事件
                eventService.putStartProcessEvent(bootProfile.getAid(), bootStatus.getState().getPid());

                // 每次启动N个后，延迟启动下一组，防止系统负载猛增，
                if (delayCount.decrementAndGet() == 0) {
                    delayCount.set(agentProperties.getLoadDelaySize());
                    Thread.sleep(agentProperties.getLoadDelaySec() * 1000L);
                }
            } catch (Exception e) {
                log.error(String.format("start action [%s]. error", bootProfile.getAid()), e);
            }
        }
        log.debug("Load server end [action size={}].", scheduledBootMap.size());
    }

    public boolean isStarted() {
        return isStarted;
    }
}

package skynet.platform.agent.core.core;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.boot.AppContext;
import skynet.platform.agent.core.config.AgentProperties;
import skynet.platform.agent.core.domain.EventItem;
import skynet.platform.agent.core.domain.HealthResult;
import skynet.platform.agent.core.domain.ObservableQueue;

import java.io.IOException;
import java.util.List;

@Slf4j
@Service
public class EventService {

    private final ObservableQueue<EventItem> observableQueue;
    private final AppContext appContext;

    public EventService(AppContext appContext, AgentProperties agentProperties) {
        this.appContext = appContext;
        this.observableQueue = (new ObservableQueue<>(agentProperties.getOutCacheLineCount()));
    }

    public void putStartProcessEvent(String aid, long pid) throws IOException {

        log.debug("putStartProcessEvent: aid:{},pid:{}", aid, pid);
        EventItem e = new EventItem();
        e.setAction("startProcess");
        e.setAddress(appContext.getIpAddress());
        e.setApp(aid);
        e.setHostname(appContext.getHostName());
        e.setNode(appContext.getNodeName());
        e.setPid(pid);
        e.setDesc(String.format("start the process. application name: %s, process ID: %s", aid, pid));

        observableQueue.put(e);
    }

    public void putStopProcessEvent(String aid, long pid) throws IOException {
        log.debug("putStopProcessEvent: aid:{},pid:{}", aid, pid);

        EventItem e = new EventItem();
        e.setAction("stopProcess");
        e.setAddress(appContext.getIpAddress());
        e.setApp(aid);
        e.setHostname(appContext.getHostName());
        e.setNode(appContext.getNodeName());
        e.setPid(pid);
        e.setDesc(String.format("process terminated, application name: %s, process ID: %s", aid, pid));

        observableQueue.put(e);
    }

    public void putKillProcessEvent(String aid, long pid) throws IOException {
        log.debug("putKillProcessEvent: aid:{},pid:{}", aid, pid);

        EventItem e = new EventItem();
        e.setAction("killProcess");
        e.setAddress(appContext.getIpAddress());
        e.setApp(aid);
        e.setHostname(appContext.getHostName());
        e.setNode(appContext.getNodeName());
        e.setPid(pid);
        e.setDesc(String.format("kill process, application name: %s, process ID: %s", aid, pid));

        observableQueue.put(e);
    }

    public void putHealthCheckEvent(HealthResult healthResult) throws IOException {
        log.debug("putHealthCheckEvent: healthResult:{}", healthResult);

        EventItem e = new EventItem();
        e.setAction("healthCheck");

        e.setAddress(appContext.getIpAddress());
        e.setApp(healthResult.getAid());
        e.setHostname(appContext.getHostName());
        e.setNode(appContext.getNodeName());
        e.setPid(healthResult.getPid());
        e.setTimestamp(healthResult.getTime());
        e.setDesc(String.format("health check %s[%s], process ID: %s [%s] %s (%s ms) %s", //
                (healthResult.isOk()) ? "success" : "fail", //
                healthResult.getFrom(), healthResult.getPid(), healthResult.getEndpoint(), //
                healthResult.getCode(), healthResult.getResponseTime(), //
                StringUtils.isBlank(healthResult.getMessage()) ? "" : healthResult.getMessage()));

        observableQueue.put(e);
    }

    public List<EventItem> getEventsAndRemove() {
        return observableQueue.getListAndRemove();
    }

    public List<EventItem> getEvents() {
        return observableQueue.getList();
    }

    /**
     * @return the observableQueue
     */
    public ObservableQueue<EventItem> getObservableQueue() {
        return observableQueue;
    }
}

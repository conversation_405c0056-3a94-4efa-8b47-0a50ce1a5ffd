#------------------------------------------------------------------------------------------------------
# åºç¡è®¾ç½® Basic Settings
#------------------------------------------------------------------------------------------------------
server.port=6230
server.servlet.session.cookie.name=skynet-agent
#------------------------------------------------------------------------------------------------------
# ç³»ç»åæ° System Parameters
#------------------------------------------------------------------------------------------------------
skynet.k8s.namespace=default
#------------------------------------------------------------------------------------------------------
# è®¤è¯ä¸å®å¨éç½® Authentication & Security Configuration
#------------------------------------------------------------------------------------------------------
skynet.auth.api-key=skynet
skynet.auth.api-secret=SKYNET_API_SECRET_PLACEHOLDER
skynet.security.enabled=true
skynet.security.base-auth.enabled=true
skynet.security.base-auth.path-patterns=${spring.cloud.config.server.prefix}/**,/skynet/config/**,/grafana/**,/actuator/prometheus
skynet.security.base-auth.ignore-patterns=
skynet.security.base-auth.actuator-security-enabled=false
skynet.security.base-auth.user.name=admin
skynet.security.base-auth.user.password=skynet2230
skynet.security.sign-auth.enabled=true
skynet.security.sign-auth.app.skynet=${skynet.auth.api-secret}
skynet.security.sign-auth.path-patterns=/skynet/agent/**,/actuator/**
skynet.security.sign-auth.ignore-patterns=/actuator/health,/actuator/prometheus
skynet.security.form-auth.enabled=false
#------------------------------------------------------------------------------------------------------
# æ¥å¿éç½® Logging Configuration
#------------------------------------------------------------------------------------------------------
logging.config=classpath:logback-xagent.xml
#------------------------------------------------------------------------------------------------------
# å¶ä»éç½® Other Settings
#------------------------------------------------------------------------------------------------------
# disabled agent swagger
skynet.api.swagger2.enabled=false
i18n.language=zh_CN
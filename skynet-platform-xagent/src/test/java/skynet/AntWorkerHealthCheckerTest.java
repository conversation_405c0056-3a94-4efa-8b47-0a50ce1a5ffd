//package skynet;
//
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import skynet.boot.core.domain.AntNodeState;
//import skynet.boot.core.domain.AntWorkerDownObserver;
//import skynet.boot.test.AntWorkerTestApp;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = AntWorkerTestApp.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
//public class AntWorkerHealthCheckerTest {
//
//	static {
//		// Action服务名称
//		System.setProperty("skynet.current.action", "ant-demo-v10@ant");
//		System.setProperty("skynet.current.ip", "127.0.0.1");
//		System.setProperty("server.port", "2201");
//		// 必须设置 true
//		System.setProperty("skynet.test.enabled", "true");
//	}
//
//	/**
//	 * 具体的业务测试
//	 * 
//	 * @throws Exception
//	 */
//	@Test
//	public void get() throws Exception {
//
//		AntNodeState antNodeState = new AntNodeState();
//		antNodeState.port = 2235;
//
//		AntWorkerDownObserver antWorkerDownObserver = new AntWorkerDownObserver() {
//
//			@Override
//			public void onDown() {
//				System.out.println("down");
//			}
//		};
//		AntWorkerHealthChecker antWorkerHealthChecker = new AntWorkerHealthChecker(antNodeState, antWorkerDownObserver);
//		antWorkerHealthChecker.start();
//		Thread.sleep(1000000);
//		antWorkerHealthChecker.close();
//	}
//
//}
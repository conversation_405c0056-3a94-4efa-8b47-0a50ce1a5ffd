package skynet.ant;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class ScheduledExecutorServiceTest {

    public static void main(String[] args) throws InterruptedException {
        ScheduledExecutorService scheduExec = Executors.newScheduledThreadPool(1);
        int timeout = 1000;
        final String jobID = "";
        final Map<String, Future<?>> futures = new HashMap<>();

        Future<?> future = scheduExec.scheduleWithFixedDelay(() -> {
            try {
                System.out.println("run");
                Thread.sleep(2000);
                System.out.println("sleep.");

                // Future<?> future = futures.get(jobID);
                // if (future != null)
                // future.cancel(true);

            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }, 0, (int) (timeout * 0.98), TimeUnit.MILLISECONDS);

        Thread.sleep(10);
        futures.put(jobID, future);
        System.err.println(future.isDone());

        future.cancel(true);
        System.err.println(future.isCancelled());

        System.err.println(scheduExec.isShutdown());
        System.err.println(scheduExec.isTerminated());

        Thread.sleep(10000);

        System.err.println("-------------");

        scheduExec.awaitTermination(200, TimeUnit.MILLISECONDS);
        // scheduExec.shutdown();

        System.err.println(scheduExec.isShutdown());
        System.err.println(scheduExec.isTerminated());

        Thread.sleep(100000);

    }
}
